#!/usr/bin/env python3
"""
Quick test to verify the setup is working.
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """Test that all imports work."""
    try:
        print("🧪 Testing imports...")
        
        # Test core imports
        from src.metadata_fields.core.config import get_settings
        print("✅ Core config import works")
        
        # Test database imports
        from src.metadata_fields.database.models import Base, EntityType
        print("✅ Database models import works")
        
        # Test API imports
        from src.metadata_fields.main import app
        print("✅ FastAPI app import works")
        
        # Test settings
        settings = get_settings()
        print(f"✅ Settings loaded: {settings.APP_NAME}")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_database_connection():
    """Test database connection."""
    try:
        print("\n🔗 Testing database connection...")
        import asyncio
        from src.metadata_fields.database.connection import AsyncSessionLocal
        from sqlalchemy import text
        
        async def test_db():
            try:
                async with AsyncSessionLocal() as session:
                    result = await session.execute(text("SELECT 1"))
                    return result.scalar() == 1
            except Exception as e:
                print(f"Database connection error: {e}")
                return False
        
        if asyncio.run(test_db()):
            print("✅ Database connection successful!")
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Metadata Fields - Setup Verification")
    print("=" * 50)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test database (optional)
    try:
        if not test_database_connection():
            print("⚠️  Database connection failed (run setup first)")
    except:
        print("⚠️  Database test skipped (run setup first)")
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Setup verification passed!")
        print("\nNext steps:")
        print("1. Set up database: python scripts/simple_setup.py")
        print("2. Start server: python scripts/start_server.py")
        print("3. Test API: ./scripts/test_api.sh")
    else:
        print("❌ Setup verification failed!")
        print("\nTroubleshooting:")
        print("1. Activate virtual environment: source venv/bin/activate")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Install package: pip install -e .")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
