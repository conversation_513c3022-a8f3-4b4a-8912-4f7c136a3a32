# Makefile for Metadata Fields API

.PHONY: help install dev-install test lint format run demo clean setup-db migrate

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install production dependencies"
	@echo "  dev-install  - Install development dependencies"
	@echo "  test         - Run tests"
	@echo "  lint         - Run linting"
	@echo "  format       - Format code"
	@echo "  run          - Start the API server"
	@echo "  demo         - Run the demo scenario"
	@echo "  setup-db     - Set up database with sample data"
	@echo "  init-db      - Initialize database tables only"
	@echo "  migrate      - Run database migrations"
	@echo "  test-api     - Run API tests"
	@echo "  test-setup   - Test that setup is working"
	@echo "  check-env    - Check virtual environment"
	@echo "  clean        - Clean up temporary files"

# Installation
install:
	pip install -r requirements.txt

dev-install:
	pip install -r requirements.txt
	pip install -e ".[dev]"

# Testing
test:
	pytest tests/ -v

test-coverage:
	pytest tests/ --cov=src/metadata_fields --cov-report=html

# Code quality
lint:
	mypy src/
	black --check src/ tests/
	isort --check-only src/ tests/

format:
	black src/ tests/
	isort src/ tests/

# Database
setup-db:
	python scripts/simple_setup.py

setup-db-full:
	python scripts/setup_database.py

migrate:
	alembic revision --autogenerate -m "Auto migration"
	alembic upgrade head

init-db:
	alembic upgrade head

create-tables:
	python scripts/create_tables.py

# Running
run:
	python scripts/start_server.py

demo:
	python scripts/run_demo.py

test-api:
	./scripts/test_api.sh

test-setup:
	python test_setup.py

check-env:
	@echo "Checking virtual environment..."
	@which python
	@python -c "import sys; print('Python version:', sys.version)"
	@if python -c "import venv" 2>/dev/null; then echo "✅ Virtual environment support available"; else echo "❌ Virtual environment support missing"; fi

# Development
dev: format lint test

# Cleanup
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/
