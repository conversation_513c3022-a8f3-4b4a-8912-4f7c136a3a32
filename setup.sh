#!/bin/bash

# Complete setup script for Metadata Fields API
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Metadata Fields API - Complete Setup${NC}"
echo "=================================================="

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

# Check if Python 3.9+ is available
echo -e "${BLUE}🔍 Checking Python version...${NC}"
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.9"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    print_status 0 "Python $python_version found"
else
    echo -e "${RED}❌ Python 3.9+ required, found $python_version${NC}"
    exit 1
fi

# Step 1: Create virtual environment
echo -e "\n${BLUE}📦 Step 1: Setting up virtual environment...${NC}"
if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_status $? "Virtual environment created"
else
    print_status 0 "Virtual environment already exists"
fi

# Step 2: Activate virtual environment and install dependencies
echo -e "\n${BLUE}📚 Step 2: Installing dependencies...${NC}"
source venv/bin/activate

# Upgrade pip first
pip install --upgrade pip > /dev/null 2>&1
print_status $? "Pip upgraded"

# Install requirements
pip install -r requirements.txt > /dev/null 2>&1
print_status $? "Dependencies installed"

# Install package in development mode
pip install -e . > /dev/null 2>&1
print_status $? "Package installed in development mode"

# Step 3: Check PostgreSQL
echo -e "\n${BLUE}🐘 Step 3: Checking PostgreSQL...${NC}"
if command -v psql >/dev/null 2>&1; then
    print_status 0 "PostgreSQL client found"
    
    # Check if PostgreSQL server is running
    if pg_isready >/dev/null 2>&1; then
        print_status 0 "PostgreSQL server is running"
    else
        echo -e "${YELLOW}⚠️  PostgreSQL server is not running${NC}"
        echo "Please start PostgreSQL and run this script again"
        echo "On macOS: brew services start postgresql"
        echo "On Ubuntu: sudo systemctl start postgresql"
        exit 1
    fi
else
    echo -e "${RED}❌ PostgreSQL not found${NC}"
    echo "Please install PostgreSQL:"
    echo "On macOS: brew install postgresql"
    echo "On Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    exit 1
fi

# Step 4: Create database
echo -e "\n${BLUE}🗄️  Step 4: Setting up database...${NC}"
DB_NAME="metadata_fields"
DB_USER="metadata_user"
DB_PASS="metadata_pass"

# Check if database exists
if psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
    print_status 0 "Database '$DB_NAME' already exists"
else
    createdb $DB_NAME
    print_status $? "Database '$DB_NAME' created"
fi

# Create user if it doesn't exist
if psql -t -c "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER'" | grep -q 1; then
    print_status 0 "Database user '$DB_USER' already exists"
else
    psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASS';" > /dev/null 2>&1
    psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" > /dev/null 2>&1
    print_status $? "Database user '$DB_USER' created"
fi

# Step 5: Set up environment file
echo -e "\n${BLUE}⚙️  Step 5: Setting up environment configuration...${NC}"
if [ ! -f ".env" ]; then
    cp .env.example .env
    
    # Update the .env file with the database URL
    sed -i.bak "s|DATABASE_URL=.*|DATABASE_URL=postgresql+asyncpg://$DB_USER:$DB_PASS@localhost:5432/$DB_NAME|" .env
    rm .env.bak
    
    print_status 0 "Environment file created and configured"
else
    print_status 0 "Environment file already exists"
fi

# Step 6: Initialize Alembic if needed
echo -e "\n${BLUE}🔧 Step 6: Setting up database migrations...${NC}"
if [ ! -d "src/metadata_fields/database/migrations/versions" ]; then
    mkdir -p src/metadata_fields/database/migrations/versions
    print_status 0 "Migration directory created"
fi

# Step 7: Create initial migration
echo -e "\n${BLUE}📝 Step 7: Creating database migration...${NC}"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
alembic revision --autogenerate -m "Initial migration" > /dev/null 2>&1
print_status $? "Initial migration created"

# Step 8: Apply migrations
echo -e "\n${BLUE}🔄 Step 8: Applying database migrations...${NC}"
alembic upgrade head > /dev/null 2>&1
print_status $? "Database migrations applied"

# Step 9: Create sample data
echo -e "\n${BLUE}📊 Step 9: Creating sample data...${NC}"
python scripts/setup_database.py > /dev/null 2>&1
print_status $? "Sample data created"

# Step 10: Test the setup
echo -e "\n${BLUE}🧪 Step 10: Testing the setup...${NC}"
python -c "
import asyncio
from src.metadata_fields.database.connection import AsyncSessionLocal
from sqlalchemy import text

async def test_db():
    async with AsyncSessionLocal() as session:
        result = await session.execute(text('SELECT COUNT(*) FROM entity_types'))
        count = result.scalar()
        return count > 0

if asyncio.run(test_db()):
    print('Database connection and data verified')
    exit(0)
else:
    exit(1)
" > /dev/null 2>&1
print_status $? "Database connection and data verified"

echo -e "\n${GREEN}🎉 Setup completed successfully!${NC}"
echo "=================================================="
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Activate the virtual environment:"
echo "   source venv/bin/activate"
echo ""
echo "2. Start the API server:"
echo "   make run"
echo "   # or: python scripts/start_server.py"
echo ""
echo "3. Test the API:"
echo "   make test-api"
echo "   # or: ./scripts/test_api.sh"
echo ""
echo "4. Run the demo:"
echo "   make demo"
echo "   # or: python scripts/run_demo.py"
echo ""
echo -e "${BLUE}🌐 URLs (once server is running):${NC}"
echo "• API Documentation: http://localhost:8000/docs"
echo "• ReDoc Documentation: http://localhost:8000/redoc"
echo "• Health Check: http://localhost:8000/health"
echo ""
echo -e "${BLUE}🔧 Useful Commands:${NC}"
echo "• make help          - Show all available commands"
echo "• make test          - Run tests"
echo "• make lint          - Run code linting"
echo "• make format        - Format code"
echo ""
echo -e "${GREEN}Happy coding! 🚀${NC}"
