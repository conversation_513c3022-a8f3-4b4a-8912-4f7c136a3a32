#!/bin/bash

# Simple setup script that activates venv and runs setup
set -e

echo "🚀 Metadata Fields - Quick Setup"
echo "================================"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please create it first:"
    echo "   python3 -m venv venv"
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source venv/bin/activate

# Check if dependencies are installed
echo "🔍 Checking dependencies..."
if ! python -c "import fastapi" 2>/dev/null; then
    echo "📚 Installing dependencies..."
    pip install -r requirements.txt
    pip install -e .
fi

# Set up environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️ Creating environment file..."
    cp .env.example .env
    echo "✅ Created .env file (you may need to edit database settings)"
fi

# Run the setup
echo "🔧 Setting up database..."
python scripts/simple_setup.py

echo ""
echo "🎉 Setup complete! Next steps:"
echo "1. Activate virtual environment: source venv/bin/activate"
echo "2. Start the server: python scripts/start_server.py"
echo "3. Test the API: ./scripts/test_api.sh"
