# Complete Setup Guide - Metadata Fields API

## 🚀 Step-by-Step Setup Instructions

### Prerequisites
- Python 3.9+ installed
- PostgreSQL 12+ installed and running
- Git (optional, for cloning)

### Step 1: Environment Setup

```bash
# 1. Navigate to your project directory
cd /path/to/your/project

# 2. Create a Python virtual environment
python -m venv venv

# 3. Activate the virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate

# 4. Verify virtual environment is active (should show venv path)
which python
```

### Step 2: Install Dependencies

```bash
# 1. Upgrade pip
pip install --upgrade pip

# 2. Install all requirements
pip install -r requirements.txt

# 3. Install the package in development mode
pip install -e .

# 4. Verify installation
python -c "import src.metadata_fields; print('✅ Package installed successfully')"
```

### Step 3: PostgreSQL Database Setup

```bash
# 1. Create the database (run these commands in your terminal)
createdb metadata_fields

# 2. Create a database user (optional, for security)
psql -c "CREATE USER metadata_user WITH PASSWORD 'metadata_pass';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE metadata_fields TO metadata_user;"

# 3. Test the connection
psql -d metadata_fields -c "SELECT version();"
```

### Step 4: Environment Configuration

```bash
# 1. Copy the example environment file
cp .env.example .env

# 2. Edit the .env file with your database settings
# Use your preferred editor (nano, vim, code, etc.)
nano .env
```

**Edit `.env` file with these settings:**
```env
# Database Configuration
DATABASE_URL=postgresql+asyncpg://metadata_user:metadata_pass@localhost:5432/metadata_fields
DATABASE_ECHO=false

# Application Configuration
APP_NAME=Metadata Fields API
APP_VERSION=0.1.0
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production

# API Configuration
API_V1_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:8000"]

# Logging
LOG_LEVEL=INFO
```

### Step 5: Database Migration Setup

```bash
# 1. Initialize Alembic (if not already done)
alembic init src/metadata_fields/database/migrations

# 2. Create initial migration
alembic revision --autogenerate -m "Initial migration"

# 3. Apply migrations to create tables
alembic upgrade head

# 4. Verify tables were created
psql -d metadata_fields -c "\dt"
```

### Step 6: Start the Server

```bash
# Option 1: Using the startup script (recommended)
python scripts/start_server.py

# Option 2: Using uvicorn directly
uvicorn src.metadata_fields.main:app --reload --host 0.0.0.0 --port 8000

# Option 3: Using the Makefile
make run
```

### Step 7: Verify Setup

```bash
# 1. Test health endpoint
curl http://localhost:8000/health

# 2. Test API documentation
# Open in browser: http://localhost:8000/docs

# 3. Test basic API
curl http://localhost:8000/api/v1/entities/
```

## 🧪 Testing the System

### Test 1: Basic API Health Check

```bash
# Test server is running
curl -X GET "http://localhost:8000/health" \
  -H "accept: application/json"

# Expected response:
# {"status": "healthy"}
```

### Test 2: Create Entity Type

```bash
# Create Address entity
curl -X POST "http://localhost:8000/api/v1/entities/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Address",
    "description": "Address entity for multi-tenant SaaS",
    "core_fields": ["name", "address1", "city", "zipcode"],
    "metadata": {"version": "1.0.0", "created_by": "setup_test"}
  }'
```

### Test 3: Create Tenants

```bash
# Create Tenant A (ACME Corp)
curl -X POST "http://localhost:8000/api/v1/tenants/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ACME Corp",
    "description": "Manufacturing company",
    "metadata": {"industry": "manufacturing", "size": "large"}
  }'

# Create Tenant B (TechStart Inc)
curl -X POST "http://localhost:8000/api/v1/tenants/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "TechStart Inc", 
    "description": "Technology startup",
    "metadata": {"industry": "technology", "size": "small"}
  }'
```

### Test 4: Add Core Fields (Product Team)

```bash
# Add Address2 field for all tenants
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "address2",
    "type": "string",
    "scope": "core",
    "description": "Secondary address line",
    "constraints": {
      "required": false,
      "max_length": 100
    },
    "ui_config": {
      "label": "Address Line 2",
      "placeholder": "Apt, Suite, Unit, etc."
    }
  }'

# Add Country field
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "country",
    "type": "string",
    "scope": "core",
    "description": "Country code",
    "constraints": {
      "required": true,
      "max_length": 3,
      "pattern": "^[A-Z]{2,3}$",
      "default": "USA"
    }
  }'
```

### Test 5: Add Tenant-Specific Fields

```bash
# Get tenant IDs first
TENANT_A_ID=$(curl -s "http://localhost:8000/api/v1/tenants/" | jq -r '.[] | select(.name=="ACME Corp") | .id')
TENANT_B_ID=$(curl -s "http://localhost:8000/api/v1/tenants/" | jq -r '.[] | select(.name=="TechStart Inc") | .id')

# Add Building Number for ACME Corp
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: $TENANT_A_ID" \
  -d '{
    "name": "building_number",
    "type": "string",
    "scope": "tenant",
    "description": "Building number for ACME Corp addresses",
    "constraints": {
      "required": true,
      "max_length": 10,
      "pattern": "^[A-Z0-9]+$"
    }
  }'

# Add District for TechStart Inc
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: $TENANT_B_ID" \
  -d '{
    "name": "district",
    "type": "string", 
    "scope": "tenant",
    "description": "District information for TechStart addresses",
    "constraints": {
      "required": false,
      "max_length": 50
    }
  }'
```

### Test 6: View Different Tenant Perspectives

```bash
# View fields as ACME Corp (should see core + building_number)
curl -X GET "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "X-Tenant-ID: $TENANT_A_ID"

# View fields as TechStart Inc (should see core + district)
curl -X GET "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "X-Tenant-ID: $TENANT_B_ID"

# View only core fields (no tenant header)
curl -X GET "http://localhost:8000/api/v1/entities/Address/fields?scope=core"
```

### Test 7: Tenant Configuration & Overrides

```bash
# Configure field overrides for ACME Corp
curl -X PUT "http://localhost:8000/api/v1/tenants/$TENANT_A_ID/entities/Address/config" \
  -H "Content-Type: application/json" \
  -d '{
    "field_overrides": [
      {
        "field_name": "zipcode",
        "constraints": {
          "required": true,
          "pattern": "^[0-9]{5}(-[0-9]{4})?$"
        },
        "ui_config": {
          "label": "ZIP Code",
          "help_text": "5-digit ZIP or ZIP+4 format"
        },
        "required": true
      },
      {
        "field_name": "country",
        "default_value": "USA",
        "hidden": true
      }
    ],
    "ui_settings": {
      "field_order": ["name", "address1", "address2", "building_number", "city", "zipcode"],
      "theme": "compact"
    },
    "feature_flags": {
      "enable_address_validation": true,
      "require_building_number": true
    }
  }'

# Get tenant-specific view with overrides applied
curl -X GET "http://localhost:8000/api/v1/tenants/$TENANT_A_ID/entities/Address/fields"
```

### Test 8: Field Lifecycle - Promotion

```bash
# Promote building_number from tenant to core (simulate popularity)
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields/building_number/promote" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: $TENANT_A_ID" \
  -d '{
    "new_version": "1.3.0",
    "migration_strategy": "copy"
  }'

# Verify it's now a core field
curl -X GET "http://localhost:8000/api/v1/entities/Address/fields?scope=core"
```

### Test 9: Field Lifecycle - Deprecation

```bash
# Add a field that will become obsolete
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "fax_number",
    "type": "string",
    "scope": "core",
    "description": "Fax number (will be deprecated)",
    "constraints": {
      "required": false,
      "max_length": 20
    }
  }'

# Deprecate the fax field
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields/fax_number/deprecate" \
  -H "Content-Type: application/json" \
  -d '{
    "version_deprecated": "1.4.0",
    "reason": "Fax communication is no longer used",
    "grace_period_days": 90
  }'

# List fields including deprecated ones
curl -X GET "http://localhost:8000/api/v1/entities/Address/fields?include_deprecated=true"
```

### Test 10: Schema and Versioning

```bash
# Get complete entity schema
curl -X GET "http://localhost:8000/api/v1/entities/Address/schema"

# Get schema versions
curl -X GET "http://localhost:8000/api/v1/entities/Address/versions"

# Get tenant-specific schema view
curl -X GET "http://localhost:8000/api/v1/entities/Address/schema" \
  -H "X-Tenant-ID: $TENANT_A_ID"
```

## 🎯 Practical Use Cases to Test

### Use Case 1: E-commerce Platform
```bash
# Create Product entity
curl -X POST "http://localhost:8000/api/v1/entities/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Product",
    "description": "Product catalog entity",
    "core_fields": ["name", "price", "description", "sku"]
  }'

# Add core fields
curl -X POST "http://localhost:8000/api/v1/entities/Product/fields" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "category",
    "type": "string",
    "scope": "core",
    "description": "Product category",
    "constraints": {
      "required": true,
      "enum_values": ["electronics", "clothing", "books", "home"]
    }
  }'

# Tenant-specific: Add warranty field for electronics retailer
curl -X POST "http://localhost:8000/api/v1/entities/Product/fields" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: $TENANT_A_ID" \
  -d '{
    "name": "warranty_months",
    "type": "integer",
    "scope": "tenant",
    "description": "Warranty period in months",
    "constraints": {
      "required": false,
      "min_value": 0,
      "max_value": 120
    }
  }'
```

### Use Case 2: CRM System
```bash
# Create Customer entity
curl -X POST "http://localhost:8000/api/v1/entities/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Customer",
    "description": "Customer management entity",
    "core_fields": ["first_name", "last_name", "email", "phone"]
  }'

# Add industry-specific field for B2B tenant
curl -X POST "http://localhost:8000/api/v1/entities/Customer/fields" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: $TENANT_A_ID" \
  -d '{
    "name": "company_size",
    "type": "string",
    "scope": "tenant",
    "description": "Company size category",
    "constraints": {
      "required": true,
      "enum_values": ["startup", "small", "medium", "large", "enterprise"]
    }
  }'
```

## 🔍 Verification Commands

```bash
# Check database tables
psql -d metadata_fields -c "\dt"

# Check specific table contents
psql -d metadata_fields -c "SELECT name, scope, entity_type FROM field_definitions;"

# Check tenant configurations
psql -d metadata_fields -c "SELECT tenant_id, entity_type, field_overrides FROM tenant_configurations;"

# Monitor server logs
tail -f /path/to/logs/app.log
```

## 🐛 Troubleshooting

### Common Issues:

1. **Database Connection Error**
   ```bash
   # Check PostgreSQL is running
   pg_ctl status
   
   # Check database exists
   psql -l | grep metadata_fields
   ```

2. **Migration Issues**
   ```bash
   # Reset migrations
   alembic downgrade base
   alembic upgrade head
   ```

3. **Import Errors**
   ```bash
   # Verify virtual environment
   which python
   pip list | grep fastapi
   ```

4. **Port Already in Use**
   ```bash
   # Find process using port 8000
   lsof -i :8000
   
   # Kill the process
   kill -9 <PID>
   ```

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Server starts without errors
- ✅ Health check returns `{"status": "healthy"}`
- ✅ API docs load at http://localhost:8000/docs
- ✅ Database queries return expected results
- ✅ Tenant-specific fields show correctly
- ✅ Field promotion/deprecation works

Now you're ready to test the complete flexible field definition system!
