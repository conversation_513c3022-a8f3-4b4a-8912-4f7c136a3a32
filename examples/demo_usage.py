"""
Demonstration script showing how to use the Metadata Fields API.

This script demonstrates the key scenarios from the requirements:
1. Product team adding core fields
2. Tenants adding custom fields
3. Promoting tenant fields to core
4. Deprecating fields
"""

import asyncio
import httpx
import json
from typing import Dict, Any


class MetadataFieldsClient:
    """Client for interacting with the Metadata Fields API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    async def create_entity(self, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new entity type."""
        response = await self.client.post(
            f"{self.base_url}/api/v1/entities/",
            json=entity_data
        )
        response.raise_for_status()
        return response.json()
    
    async def create_field(
        self, 
        entity_type: str, 
        field_data: Dict[str, Any],
        tenant_id: str = None
    ) -> Dict[str, Any]:
        """Create a new field definition."""
        headers = {}
        if tenant_id:
            headers["X-Tenant-ID"] = tenant_id
        
        response = await self.client.post(
            f"{self.base_url}/api/v1/entities/{entity_type}/fields",
            json=field_data,
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    
    async def list_fields(
        self, 
        entity_type: str,
        tenant_id: str = None,
        scope: str = None
    ) -> Dict[str, Any]:
        """List fields for an entity type."""
        headers = {}
        if tenant_id:
            headers["X-Tenant-ID"] = tenant_id
        
        params = {}
        if scope:
            params["scope"] = scope
        
        response = await self.client.get(
            f"{self.base_url}/api/v1/entities/{entity_type}/fields",
            headers=headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    async def promote_field(
        self,
        entity_type: str,
        field_name: str,
        new_version: str,
        tenant_id: str
    ) -> Dict[str, Any]:
        """Promote a tenant field to core."""
        headers = {"X-Tenant-ID": tenant_id}
        promotion_data = {
            "new_version": new_version,
            "migration_strategy": "copy"
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/v1/entities/{entity_type}/fields/{field_name}/promote",
            json=promotion_data,
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    
    async def deprecate_field(
        self,
        entity_type: str,
        field_name: str,
        version_deprecated: str,
        tenant_id: str = None
    ) -> Dict[str, Any]:
        """Deprecate a field."""
        headers = {}
        if tenant_id:
            headers["X-Tenant-ID"] = tenant_id
        
        deprecation_data = {
            "version_deprecated": version_deprecated,
            "reason": "Field is no longer needed",
            "grace_period_days": 90
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/v1/entities/{entity_type}/fields/{field_name}/deprecate",
            json=deprecation_data,
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    
    async def create_tenant(self, tenant_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new tenant."""
        response = await self.client.post(
            f"{self.base_url}/api/v1/tenants/",
            json=tenant_data
        )
        response.raise_for_status()
        return response.json()
    
    async def get_tenant_entity_view(
        self,
        tenant_id: str,
        entity_type: str
    ) -> Dict[str, Any]:
        """Get tenant-specific view of an entity."""
        response = await self.client.get(
            f"{self.base_url}/api/v1/tenants/{tenant_id}/entities/{entity_type}/fields"
        )
        response.raise_for_status()
        return response.json()


async def demo_scenario():
    """Demonstrate the complete field lifecycle scenario."""
    client = MetadataFieldsClient()
    
    try:
        print("🚀 Starting Metadata Fields Demo")
        print("=" * 50)
        
        # Step 1: Create Address entity with basic core fields
        print("\n📋 Step 1: Creating Address entity with core fields")
        entity_data = {
            "name": "Address",
            "description": "Address entity for multi-tenant SaaS",
            "core_fields": ["name", "address1", "city", "zipcode"],
            "metadata": {"created_by": "product_team"}
        }
        
        entity = await client.create_entity(entity_data)
        print(f"✅ Created entity: {entity['name']}")
        
        # Step 2: Product team adds Address2 field for all tenants
        print("\n🏢 Step 2: Product team adds Address2 field (core)")
        address2_field = {
            "name": "address2",
            "type": "string",
            "scope": "core",
            "description": "Secondary address line",
            "constraints": {
                "required": False,
                "max_length": 100
            }
        }
        
        field = await client.create_field("Address", address2_field)
        print(f"✅ Created core field: {field['name']}")
        
        # Step 3: Create tenants
        print("\n👥 Step 3: Creating tenants")
        tenant_a_data = {
            "name": "ACME Corp",
            "description": "Manufacturing company",
            "metadata": {"industry": "manufacturing"}
        }
        
        tenant_b_data = {
            "name": "TechStart Inc",
            "description": "Technology startup",
            "metadata": {"industry": "technology"}
        }
        
        tenant_a = await client.create_tenant(tenant_a_data)
        tenant_b = await client.create_tenant(tenant_b_data)
        print(f"✅ Created tenants: {tenant_a['name']}, {tenant_b['name']}")
        
        # Step 4: Tenant A adds Building Number field
        print("\n🏗️  Step 4: Tenant A adds Building Number field")
        building_field = {
            "name": "building_number",
            "type": "string",
            "scope": "tenant",
            "description": "Building number for ACME Corp addresses",
            "constraints": {
                "required": True,
                "max_length": 10
            }
        }
        
        field = await client.create_field("Address", building_field, tenant_a['id'])
        print(f"✅ Created tenant field for {tenant_a['name']}: {field['name']}")
        
        # Step 5: Tenant B adds District field
        print("\n🌆 Step 5: Tenant B adds District field")
        district_field = {
            "name": "district",
            "type": "string",
            "scope": "tenant",
            "description": "District information for TechStart addresses",
            "constraints": {
                "required": False,
                "max_length": 50
            }
        }
        
        field = await client.create_field("Address", district_field, tenant_b['id'])
        print(f"✅ Created tenant field for {tenant_b['name']}: {field['name']}")
        
        # Step 6: Show different views for each tenant
        print("\n👀 Step 6: Showing tenant-specific views")
        
        # Tenant A view
        tenant_a_view = await client.get_tenant_entity_view(tenant_a['id'], "Address")
        print(f"\n{tenant_a['name']} sees {len(tenant_a_view['fields'])} fields:")
        for field in tenant_a_view['fields']:
            scope_indicator = "🌍" if field['scope'] == 'core' else "🏢"
            print(f"  {scope_indicator} {field['field_name']} ({field['field_type']})")
        
        # Tenant B view
        tenant_b_view = await client.get_tenant_entity_view(tenant_b['id'], "Address")
        print(f"\n{tenant_b['name']} sees {len(tenant_b_view['fields'])} fields:")
        for field in tenant_b_view['fields']:
            scope_indicator = "🌍" if field['scope'] == 'core' else "🏢"
            print(f"  {scope_indicator} {field['field_name']} ({field['field_type']})")
        
        # Step 7: Multiple tenants request Country field, so promote to core
        print("\n🌍 Step 7: Promoting popular field to core")
        
        # First, let's add country field for tenant A
        country_field = {
            "name": "country",
            "type": "string",
            "scope": "tenant",
            "description": "Country code",
            "constraints": {
                "required": True,
                "max_length": 3,
                "pattern": "^[A-Z]{2,3}$"
            }
        }
        
        await client.create_field("Address", country_field, tenant_a['id'])
        print(f"✅ Created country field for {tenant_a['name']}")
        
        # Now promote it to core
        promoted_field = await client.promote_field(
            "Address", "country", "1.3.0", tenant_a['id']
        )
        print(f"✅ Promoted 'country' to core field in version {promoted_field['version_added']}")
        
        # Step 8: Deprecate an obsolete field
        print("\n⚠️  Step 8: Deprecating obsolete field")
        
        # Let's add a fax field first, then deprecate it
        fax_field = {
            "name": "fax_number",
            "type": "string",
            "scope": "core",
            "description": "Fax number (deprecated)",
            "constraints": {
                "required": False,
                "max_length": 20
            }
        }
        
        await client.create_field("Address", fax_field)
        print(f"✅ Created fax_number field")
        
        deprecated_field = await client.deprecate_field("Address", "fax_number", "1.4.0")
        print(f"✅ Deprecated 'fax_number' in version {deprecated_field['version_deprecated']}")
        
        # Step 9: Final summary
        print("\n📊 Step 9: Final field summary")
        all_fields = await client.list_fields("Address", scope="all")
        
        core_fields = [f for f in all_fields['fields'] if f['scope'] == 'core']
        tenant_fields = [f for f in all_fields['fields'] if f['scope'] == 'tenant']
        deprecated_fields = [f for f in all_fields['fields'] if f['is_deprecated']]
        
        print(f"\nTotal fields: {len(all_fields['fields'])}")
        print(f"Core fields: {len(core_fields)}")
        print(f"Tenant fields: {len(tenant_fields)}")
        print(f"Deprecated fields: {len(deprecated_fields)}")
        
        print("\n🎉 Demo completed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        raise
    finally:
        await client.close()


if __name__ == "__main__":
    print("Make sure the API server is running on http://localhost:8000")
    print("Start it with: uvicorn src.metadata_fields.main:app --reload")
    print()
    
    asyncio.run(demo_scenario())
