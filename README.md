# Metadata Fields - Flexible Field Definition System

A FastAPI-based system for managing dynamic field definitions in multi-tenant SaaS applications.

## Features

- **Hybrid Schema Design**: Combines structured columns with JSONB for flexibility
- **Multi-Tenant Support**: Core fields for all tenants, custom fields per tenant
- **Field Lifecycle Management**: Promote tenant fields to core, deprecate obsolete fields
- **Version Control**: Track schema evolution over time
- **Metadata-Driven**: All field definitions stored as configuration

## Architecture

### 3-Layer Field Structure

1. **Core Fields**: Stable, structured columns for essential data
2. **Custom Attributes**: JSONB column for product-wide dynamic fields
3. **Tenant Attributes**: JSONB column for tenant-specific fields

### Directory Structure

```
src/
├── metadata_fields/
│   ├── api/                 # FastAPI routers and endpoints
│   │   ├── v1/
│   │   │   ├── entities.py  # Entity management endpoints
│   │   │   ├── fields.py    # Field definition endpoints
│   │   │   └── tenants.py   # Tenant configuration endpoints
│   │   └── dependencies.py  # API dependencies
│   ├── core/                # Core business logic
│   │   ├── config.py        # Application configuration
│   │   ├── security.py      # Authentication and authorization
│   │   └── exceptions.py    # Custom exceptions
│   ├── models/              # Pydantic models and schemas
│   │   ├── entities.py      # Entity-related models
│   │   ├── fields.py        # Field definition models
│   │   └── tenants.py       # Tenant configuration models
│   ├── services/            # Business logic services
│   │   ├── field_service.py # Field management logic
│   │   ├── tenant_service.py# Tenant configuration logic
│   │   └── validation.py    # Field validation logic
│   └── database/            # Database layer
│       ├── models.py        # SQLAlchemy models
│       ├── connection.py    # Database connection
│       └── migrations/      # Alembic migrations
├── tests/                   # Test suite
└── main.py                  # Application entry point
```

## Quick Start

### Option 1: Local Development

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Set up environment:**
```bash
cp .env.example .env
# Edit .env with your database settings
```

3. **Set up the database:**
```bash
# Using PostgreSQL (recommended)
createdb metadata_fields
alembic upgrade head

# Or use the Makefile
make setup-db
```

4. **Run the application:**
```bash
# Using the startup script
python scripts/start_server.py

# Or directly with uvicorn
uvicorn src.metadata_fields.main:app --reload

# Or using the Makefile
make run
```

### Option 2: Docker Compose

1. **Start all services:**
```bash
docker-compose up -d
```

2. **Run database migrations:**
```bash
docker-compose exec api alembic upgrade head
```

### Option 3: Quick Demo

1. **Start the server:**
```bash
make run
```

2. **In another terminal, run the demo:**
```bash
make demo
```

## API Documentation

Once running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Detailed API Docs**: [API_DOCUMENTATION.md](API_DOCUMENTATION.md)

## Example Usage

### 1. Create an Entity Type
```bash
curl -X POST "http://localhost:8000/api/v1/entities/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Address",
    "description": "Address entity for multi-tenant SaaS",
    "core_fields": ["name", "address1", "city", "zipcode"]
  }'
```

### 2. Add a Core Field (Product Team)
```bash
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "address2",
    "type": "string",
    "scope": "core",
    "description": "Secondary address line",
    "constraints": {"required": false, "max_length": 100}
  }'
```

### 3. Add a Tenant-Specific Field
```bash
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: tenant-123" \
  -d '{
    "name": "building_number",
    "type": "string",
    "scope": "tenant",
    "description": "Building number for this tenant",
    "constraints": {"required": true, "max_length": 10}
  }'
```

### 4. List Fields for a Tenant
```bash
curl -X GET "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "X-Tenant-ID: tenant-123"
```

### 5. Promote Tenant Field to Core
```bash
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields/building_number/promote" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: tenant-123" \
  -d '{
    "new_version": "1.3.0",
    "migration_strategy": "copy"
  }'
```

## Available Commands

Use the Makefile for common tasks:

```bash
make help          # Show all available commands
make install       # Install dependencies
make dev-install   # Install with dev dependencies
make test          # Run tests
make lint          # Run linting
make format        # Format code
make run           # Start the server
make demo          # Run the demo scenario
make setup-db      # Set up database
make migrate       # Run migrations
make clean         # Clean up temporary files
```

## Key Concepts

### Field Scopes
- **core**: Available to all tenants
- **tenant**: Specific to one tenant

### Field Lifecycle
1. Create tenant-specific field
2. Promote to core when popular
3. Deprecate when obsolete
4. Remove after grace period

### Tenant Configuration
- Field overrides (validation, defaults)
- UI customization (labels, order)
- Feature flags (enable/disable fields)
