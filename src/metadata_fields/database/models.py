"""
SQLAlchemy database models for the metadata fields system.
"""

import uuid
from datetime import datetime
from typing import Any, Dict

from sqlalchemy import (
    Boolean, Column, DateTime, ForeignKey, Index, String, Text, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .connection import Base


def generate_uuid() -> str:
    """Generate a UUID string."""
    return str(uuid.uuid4())


class TimestampMixin:
    """Mixin for created_at and updated_at timestamps."""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        onupdate=func.now(), 
        nullable=False
    )


class EntityType(Base, TimestampMixin):
    """Entity type model."""
    __tablename__ = "entity_types"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text)
    core_fields = Column(JSONB, default=list)
    meta_data = Column(JSONB, default=dict)
    
    # Relationships
    field_definitions = relationship("FieldDefinition", back_populates="entity_type_rel")
    tenant_configurations = relationship("TenantConfiguration", back_populates="entity_type_rel")
    schema_versions = relationship("SchemaVersion", back_populates="entity_type_rel")


class Tenant(Base, TimestampMixin):
    """Tenant model."""
    __tablename__ = "tenants"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    meta_data = Column(JSONB, default=dict)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    field_definitions = relationship("FieldDefinition", back_populates="tenant_rel")
    tenant_configurations = relationship("TenantConfiguration", back_populates="tenant_rel")
    
    # Indexes
    __table_args__ = (
        Index("idx_tenant_name", "name"),
        Index("idx_tenant_active", "is_active"),
    )


class FieldDefinition(Base, TimestampMixin):
    """Field definition model."""
    __tablename__ = "field_definitions"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    name = Column(String(100), nullable=False)
    entity_type = Column(String(100), ForeignKey("entity_types.name"), nullable=False)
    type = Column(String(50), nullable=False)
    scope = Column(String(20), nullable=False)  # 'core' or 'tenant'
    description = Column(Text)
    constraints = Column(JSONB, default=dict)
    ui_config = Column(JSONB, default=dict)
    tenant_id = Column(String(36), ForeignKey("tenants.id"), nullable=True)
    version_added = Column(String(20), nullable=False)
    version_deprecated = Column(String(20), nullable=True)
    is_deprecated = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    entity_type_rel = relationship("EntityType", back_populates="field_definitions")
    tenant_rel = relationship("Tenant", back_populates="field_definitions")
    
    # Constraints and indexes
    __table_args__ = (
        UniqueConstraint("name", "entity_type", "tenant_id", name="uq_field_entity_tenant"),
        Index("idx_field_entity_scope", "entity_type", "scope"),
        Index("idx_field_tenant", "tenant_id"),
        Index("idx_field_deprecated", "is_deprecated"),
    )


class TenantConfiguration(Base, TimestampMixin):
    """Tenant configuration model."""
    __tablename__ = "tenant_configurations"

    id = Column(String(36), primary_key=True, default=generate_uuid)
    tenant_id = Column(String(36), ForeignKey("tenants.id"), nullable=False)
    entity_type = Column(String(100), ForeignKey("entity_types.name"), nullable=False)
    field_overrides = Column(JSONB, default=list)
    ui_settings = Column(JSONB, default=dict)
    feature_flags = Column(JSONB, default=dict)

    # Relationships
    tenant_rel = relationship("Tenant", back_populates="tenant_configurations")
    entity_type_rel = relationship("EntityType", back_populates="tenant_configurations")

    # Constraints and indexes
    __table_args__ = (
        UniqueConstraint("tenant_id", "entity_type", name="uq_tenant_entity_config"),
        Index("idx_config_tenant", "tenant_id"),
        Index("idx_config_entity", "entity_type"),
    )


class SchemaVersion(Base, TimestampMixin):
    """Schema version model."""
    __tablename__ = "schema_versions"

    id = Column(String(36), primary_key=True, default=generate_uuid)
    entity_type = Column(String(100), ForeignKey("entity_types.name"), nullable=False)
    version = Column(String(20), nullable=False)
    description = Column(Text)
    changes = Column(JSONB, default=list)
    is_current = Column(Boolean, default=False, nullable=False)

    # Relationships
    entity_type_rel = relationship("EntityType", back_populates="schema_versions")

    # Constraints and indexes
    __table_args__ = (
        UniqueConstraint("entity_type", "version", name="uq_entity_version"),
        Index("idx_version_entity", "entity_type"),
        Index("idx_version_current", "is_current"),
    )
