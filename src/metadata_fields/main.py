"""
Main FastAPI application for the Metadata Fields system.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .core.config import get_settings
from .api.v1 import entities, fields, tenants

settings = get_settings()

app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="Flexible field definition system for multi-tenant SaaS applications",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(
    entities.router,
    prefix=f"{settings.API_V1_PREFIX}/entities",
    tags=["entities"]
)

app.include_router(
    fields.router,
    prefix=f"{settings.API_V1_PREFIX}/entities",
    tags=["fields"]
)

app.include_router(
    tenants.router,
    prefix=f"{settings.API_V1_PREFIX}/tenants",
    tags=["tenants"]
)


@app.get("/")
async def root():
    """Root endpoint with basic API information."""
    return {
        "name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.metadata_fields.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
