"""
API dependencies for the metadata fields system.
"""

from typing import Optional

from fastapi import Depends, HTTPException, Header, status
from sqlalchemy.ext.asyncio import AsyncSession

from ..database.connection import get_db
from ..core.exceptions import TenantNotFoundError


async def get_current_tenant_id(
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
) -> Optional[str]:
    """
    Extract tenant ID from request headers.
    
    Args:
        x_tenant_id: Tenant ID from X-Tenant-ID header
        
    Returns:
        Tenant ID if provided
    """
    return x_tenant_id


async def require_tenant_id(
    tenant_id: Optional[str] = Depends(get_current_tenant_id)
) -> str:
    """
    Require tenant ID to be present.
    
    Args:
        tenant_id: Tenant ID from dependency
        
    Returns:
        Tenant ID
        
    Raises:
        HTTPException: If tenant ID is not provided
    """
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header is required"
        )
    return tenant_id


async def get_db_session() -> AsyncSession:
    """Get database session dependency."""
    async for session in get_db():
        yield session


class CommonQueryParams:
    """Common query parameters for list endpoints."""
    
    def __init__(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ):
        self.skip = skip
        self.limit = min(limit, 1000)  # Cap at 1000
        self.search = search
