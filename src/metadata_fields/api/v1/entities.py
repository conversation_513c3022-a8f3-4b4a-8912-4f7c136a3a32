"""
API endpoints for entity management.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ...database.connection import get_db
from ...database.models import EntityType as EntityTypeModel
from ...models.entities import (
    EntityType, EntityTypeCreate, EntityTypeUpdate, EntityListResponse,
    EntitySchema, SchemaVersionListResponse
)
from ...services.entity_service import EntityService
from ..dependencies import CommonQueryParams, get_current_tenant_id

router = APIRouter()


@router.get("/", response_model=EntityListResponse)
async def list_entities(
    commons: CommonQueryParams = Depends(),
    db: AsyncSession = Depends(get_db)
) -> EntityListResponse:
    """
    List all entity types.
    
    Args:
        commons: Common query parameters
        db: Database session
        
    Returns:
        List of entity types
    """
    service = EntityService(db)
    entities, total = await service.list_entities(
        skip=commons.skip,
        limit=commons.limit,
        search=commons.search
    )
    
    return EntityListResponse(entities=entities, total=total)


@router.post("/", response_model=EntityType, status_code=status.HTTP_201_CREATED)
async def create_entity(
    entity_data: EntityTypeCreate,
    db: AsyncSession = Depends(get_db)
) -> EntityType:
    """
    Create a new entity type.
    
    Args:
        entity_data: Entity type creation data
        db: Database session
        
    Returns:
        Created entity type
    """
    service = EntityService(db)
    return await service.create_entity(entity_data)


@router.get("/{entity_type}", response_model=EntityType)
async def get_entity(
    entity_type: str,
    db: AsyncSession = Depends(get_db)
) -> EntityType:
    """
    Get a specific entity type.
    
    Args:
        entity_type: Entity type name
        db: Database session
        
    Returns:
        Entity type details
    """
    service = EntityService(db)
    entity = await service.get_entity(entity_type)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Entity type '{entity_type}' not found"
        )
    return entity


@router.put("/{entity_type}", response_model=EntityType)
async def update_entity(
    entity_type: str,
    entity_data: EntityTypeUpdate,
    db: AsyncSession = Depends(get_db)
) -> EntityType:
    """
    Update an entity type.
    
    Args:
        entity_type: Entity type name
        entity_data: Entity type update data
        db: Database session
        
    Returns:
        Updated entity type
    """
    service = EntityService(db)
    entity = await service.update_entity(entity_type, entity_data)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Entity type '{entity_type}' not found"
        )
    return entity


@router.delete("/{entity_type}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_entity(
    entity_type: str,
    db: AsyncSession = Depends(get_db)
) -> None:
    """
    Delete an entity type.
    
    Args:
        entity_type: Entity type name
        db: Database session
    """
    service = EntityService(db)
    success = await service.delete_entity(entity_type)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Entity type '{entity_type}' not found"
        )


@router.get("/{entity_type}/schema", response_model=EntitySchema)
async def get_entity_schema(
    entity_type: str,
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
) -> EntitySchema:
    """
    Get complete entity schema including all fields.
    
    Args:
        entity_type: Entity type name
        tenant_id: Optional tenant ID for tenant-specific view
        db: Database session
        
    Returns:
        Complete entity schema
    """
    service = EntityService(db)
    schema = await service.get_entity_schema(entity_type, tenant_id)
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Entity type '{entity_type}' not found"
        )
    return schema


@router.get("/{entity_type}/versions", response_model=SchemaVersionListResponse)
async def list_schema_versions(
    entity_type: str,
    db: AsyncSession = Depends(get_db)
) -> SchemaVersionListResponse:
    """
    List all schema versions for an entity type.
    
    Args:
        entity_type: Entity type name
        db: Database session
        
    Returns:
        List of schema versions
    """
    service = EntityService(db)
    versions, current_version = await service.list_schema_versions(entity_type)
    
    return SchemaVersionListResponse(
        versions=versions,
        current_version=current_version,
        entity_type=entity_type
    )
