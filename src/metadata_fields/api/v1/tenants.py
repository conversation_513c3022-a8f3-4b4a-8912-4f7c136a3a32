"""
API endpoints for tenant configuration management.
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from ...database.connection import get_db
from ...models.tenants import (
    Tenant, TenantCreate, TenantUpdate,
    TenantConfiguration, TenantConfigurationCreate, TenantConfigurationUpdate,
    TenantEntityView
)
from ...services.tenant_service import TenantService
from ..dependencies import CommonQueryParams, require_tenant_id

router = APIRouter()


@router.get("/", response_model=List[Tenant])
async def list_tenants(
    commons: CommonQueryParams = Depends(),
    db: AsyncSession = Depends(get_db)
) -> List[Tenant]:
    """
    List all tenants.
    
    Args:
        commons: Common query parameters
        db: Database session
        
    Returns:
        List of tenants
    """
    service = TenantService(db)
    tenants, _ = await service.list_tenants(
        skip=commons.skip,
        limit=commons.limit,
        search=commons.search
    )
    return tenants


@router.post("/", response_model=Tenant, status_code=status.HTTP_201_CREATED)
async def create_tenant(
    tenant_data: TenantCreate,
    db: AsyncSession = Depends(get_db)
) -> Tenant:
    """
    Create a new tenant.
    
    Args:
        tenant_data: Tenant creation data
        db: Database session
        
    Returns:
        Created tenant
    """
    service = TenantService(db)
    return await service.create_tenant(tenant_data)


@router.get("/{tenant_id}", response_model=Tenant)
async def get_tenant(
    tenant_id: str,
    db: AsyncSession = Depends(get_db)
) -> Tenant:
    """
    Get a specific tenant.
    
    Args:
        tenant_id: Tenant ID
        db: Database session
        
    Returns:
        Tenant details
    """
    service = TenantService(db)
    tenant = await service.get_tenant(tenant_id)
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tenant '{tenant_id}' not found"
        )
    return tenant


@router.put("/{tenant_id}", response_model=Tenant)
async def update_tenant(
    tenant_id: str,
    tenant_data: TenantUpdate,
    db: AsyncSession = Depends(get_db)
) -> Tenant:
    """
    Update a tenant.
    
    Args:
        tenant_id: Tenant ID
        tenant_data: Tenant update data
        db: Database session
        
    Returns:
        Updated tenant
    """
    service = TenantService(db)
    tenant = await service.update_tenant(tenant_id, tenant_data)
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tenant '{tenant_id}' not found"
        )
    return tenant


@router.delete("/{tenant_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tenant(
    tenant_id: str,
    db: AsyncSession = Depends(get_db)
) -> None:
    """
    Delete a tenant.
    
    Args:
        tenant_id: Tenant ID
        db: Database session
    """
    service = TenantService(db)
    success = await service.delete_tenant(tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tenant '{tenant_id}' not found"
        )


@router.get("/{tenant_id}/entities/{entity_type}/config", response_model=TenantConfiguration)
async def get_tenant_config(
    tenant_id: str,
    entity_type: str,
    db: AsyncSession = Depends(get_db)
) -> TenantConfiguration:
    """
    Get tenant configuration for an entity type.
    
    Args:
        tenant_id: Tenant ID
        entity_type: Entity type name
        db: Database session
        
    Returns:
        Tenant configuration
    """
    service = TenantService(db)
    config = await service.get_tenant_configuration(tenant_id, entity_type)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Configuration not found for tenant '{tenant_id}' and entity '{entity_type}'"
        )
    return config


@router.put("/{tenant_id}/entities/{entity_type}/config", response_model=TenantConfiguration)
async def update_tenant_config(
    tenant_id: str,
    entity_type: str,
    config_data: TenantConfigurationUpdate,
    db: AsyncSession = Depends(get_db)
) -> TenantConfiguration:
    """
    Update tenant configuration for an entity type.
    
    Args:
        tenant_id: Tenant ID
        entity_type: Entity type name
        config_data: Configuration update data
        db: Database session
        
    Returns:
        Updated tenant configuration
    """
    service = TenantService(db)
    config = await service.update_tenant_configuration(tenant_id, entity_type, config_data)
    if not config:
        # Create new configuration if it doesn't exist
        create_data = TenantConfigurationCreate(
            entity_type=entity_type,
            **config_data.dict(exclude_unset=True)
        )
        config = await service.create_tenant_configuration(tenant_id, create_data)
    
    return config


@router.get("/{tenant_id}/entities/{entity_type}/fields", response_model=TenantEntityView)
async def get_tenant_entity_view(
    tenant_id: str,
    entity_type: str,
    db: AsyncSession = Depends(get_db)
) -> TenantEntityView:
    """
    Get tenant-specific view of an entity with all overrides applied.
    
    Args:
        tenant_id: Tenant ID
        entity_type: Entity type name
        db: Database session
        
    Returns:
        Tenant entity view with overrides applied
    """
    service = TenantService(db)
    view = await service.get_tenant_entity_view(tenant_id, entity_type)
    if not view:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Entity '{entity_type}' not found or tenant '{tenant_id}' not found"
        )
    return view
