"""
API endpoints for field definition management.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from ...database.connection import get_db
from ...models.fields import (
    FieldDefinition, FieldDefinitionCreate, FieldDefinitionUpdate,
    FieldListResponse, FieldPromotionRequest, FieldDeprecationRequest,
    FieldScope
)
from ...services.field_service import FieldService
from ..dependencies import CommonQueryParams, get_current_tenant_id

router = APIRouter()


@router.get("/{entity_type}/fields", response_model=FieldListResponse)
async def list_fields(
    entity_type: str,
    scope: Optional[FieldScope] = Query(None, description="Filter by scope (core, tenant, all)"),
    include_deprecated: bool = Query(False, description="Include deprecated fields"),
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    commons: CommonQueryParams = Depends(),
    db: AsyncSession = Depends(get_db)
) -> FieldListResponse:
    """
    Retrieve all field definitions for an entity type, respecting tenant overrides.
    
    Args:
        entity_type: Entity type name
        scope: Filter by scope (core, tenant, all)
        include_deprecated: Include deprecated fields
        tenant_id: Tenant ID from header
        commons: Common query parameters
        db: Database session
        
    Returns:
        List of field definitions with their complete metadata
    """
    service = FieldService(db)
    fields, total = await service.list_fields(
        entity_type=entity_type,
        scope=scope,
        include_deprecated=include_deprecated,
        tenant_id=tenant_id,
        skip=commons.skip,
        limit=commons.limit,
        search=commons.search
    )
    
    return FieldListResponse(
        fields=fields,
        total=total,
        entity_type=entity_type,
        tenant_id=tenant_id
    )


@router.get("/{entity_type}/fields/{field_name}", response_model=FieldDefinition)
async def get_field(
    entity_type: str,
    field_name: str,
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
) -> FieldDefinition:
    """
    Get a specific field's definition.
    
    Args:
        entity_type: Entity type name
        field_name: Field name
        tenant_id: Tenant ID from header
        db: Database session
        
    Returns:
        Single field definition object
    """
    service = FieldService(db)
    field = await service.get_field(entity_type, field_name, tenant_id)
    if not field:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Field '{field_name}' not found for entity '{entity_type}'"
        )
    return field


@router.post("/{entity_type}/fields", response_model=FieldDefinition, status_code=status.HTTP_201_CREATED)
async def create_field(
    entity_type: str,
    field_data: FieldDefinitionCreate,
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
) -> FieldDefinition:
    """
    Create a new field definition.
    
    Args:
        entity_type: Entity type name
        field_data: Field definition creation data
        tenant_id: Tenant ID from header
        db: Database session
        
    Returns:
        Created field definition
    """
    service = FieldService(db)
    
    # For tenant-scoped fields, use tenant_id from header if not provided
    if field_data.scope == FieldScope.TENANT and not field_data.tenant_id:
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="X-Tenant-ID header is required for tenant-scoped fields"
            )
        field_data.tenant_id = tenant_id
    
    return await service.create_field(entity_type, field_data)


@router.put("/{entity_type}/fields/{field_name}", response_model=FieldDefinition)
async def update_field(
    entity_type: str,
    field_name: str,
    field_data: FieldDefinitionUpdate,
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
) -> FieldDefinition:
    """
    Update a field definition.
    
    Args:
        entity_type: Entity type name
        field_name: Field name
        field_data: Field definition update data
        tenant_id: Tenant ID from header
        db: Database session
        
    Returns:
        Updated field definition
    """
    service = FieldService(db)
    field = await service.update_field(entity_type, field_name, field_data, tenant_id)
    if not field:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Field '{field_name}' not found for entity '{entity_type}'"
        )
    return field


@router.delete("/{entity_type}/fields/{field_name}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_field(
    entity_type: str,
    field_name: str,
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
) -> None:
    """
    Delete a field definition.
    
    Args:
        entity_type: Entity type name
        field_name: Field name
        tenant_id: Tenant ID from header
        db: Database session
    """
    service = FieldService(db)
    success = await service.delete_field(entity_type, field_name, tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Field '{field_name}' not found for entity '{entity_type}'"
        )


@router.post("/{entity_type}/fields/{field_name}/promote", response_model=FieldDefinition)
async def promote_field(
    entity_type: str,
    field_name: str,
    promotion_data: FieldPromotionRequest,
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
) -> FieldDefinition:
    """
    Promote a tenant-specific field to core.
    
    Args:
        entity_type: Entity type name
        field_name: Field name
        promotion_data: Promotion request data
        tenant_id: Tenant ID from header
        db: Database session
        
    Returns:
        Promoted field definition
    """
    service = FieldService(db)
    field = await service.promote_field(entity_type, field_name, promotion_data, tenant_id)
    if not field:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Field '{field_name}' not found for entity '{entity_type}'"
        )
    return field


@router.post("/{entity_type}/fields/{field_name}/deprecate", response_model=FieldDefinition)
async def deprecate_field(
    entity_type: str,
    field_name: str,
    deprecation_data: FieldDeprecationRequest,
    tenant_id: Optional[str] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
) -> FieldDefinition:
    """
    Deprecate a field.
    
    Args:
        entity_type: Entity type name
        field_name: Field name
        deprecation_data: Deprecation request data
        tenant_id: Tenant ID from header
        db: Database session
        
    Returns:
        Deprecated field definition
    """
    service = FieldService(db)
    field = await service.deprecate_field(entity_type, field_name, deprecation_data, tenant_id)
    if not field:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Field '{field_name}' not found for entity '{entity_type}'"
        )
    return field
