"""
Pydantic models for tenant configurations and overrides.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .fields import FieldConstraints


class FieldOverride(BaseModel):
    """Field override configuration for tenants."""
    field_name: str = Field(..., description="Name of the field to override")
    constraints: Optional[FieldConstraints] = Field(None, description="Override constraints")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="Override UI configuration")
    hidden: Optional[bool] = Field(None, description="Whether to hide the field")
    required: Optional[bool] = Field(None, description="Override required setting")
    default_value: Optional[Any] = Field(None, description="Override default value")


class TenantConfigurationBase(BaseModel):
    """Base tenant configuration model."""
    entity_type: str = Field(..., description="Entity type name")
    field_overrides: List[FieldOverride] = Field(
        default_factory=list, 
        description="Field override configurations"
    )
    ui_settings: Optional[Dict[str, Any]] = Field(
        None, 
        description="UI customization settings"
    )
    feature_flags: Optional[Dict[str, bool]] = Field(
        None, 
        description="Feature flags for enabling/disabling fields"
    )


class TenantConfigurationCreate(TenantConfigurationBase):
    """Model for creating tenant configurations."""
    pass


class TenantConfigurationUpdate(BaseModel):
    """Model for updating tenant configurations."""
    field_overrides: Optional[List[FieldOverride]] = None
    ui_settings: Optional[Dict[str, Any]] = None
    feature_flags: Optional[Dict[str, bool]] = None


class TenantConfiguration(TenantConfigurationBase):
    """Complete tenant configuration model."""
    id: str = Field(..., description="Configuration ID")
    tenant_id: str = Field(..., description="Tenant ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


class TenantBase(BaseModel):
    """Base tenant model."""
    name: str = Field(..., description="Tenant name")
    description: Optional[str] = Field(None, description="Tenant description")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_active: bool = Field(True, description="Whether tenant is active")


class TenantCreate(TenantBase):
    """Model for creating tenants."""
    pass


class TenantUpdate(BaseModel):
    """Model for updating tenants."""
    name: Optional[str] = None
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class Tenant(TenantBase):
    """Complete tenant model."""
    id: str = Field(..., description="Tenant ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        """Create from ORM object, mapping meta_data to metadata."""
        data = {
            "id": obj.id,
            "name": obj.name,
            "description": obj.description,
            "metadata": obj.meta_data,  # Map meta_data to metadata
            "is_active": obj.is_active,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at
        }
        return cls(**data)


class TenantFieldView(BaseModel):
    """Tenant-specific view of fields with overrides applied."""
    field_name: str
    field_type: str
    scope: str
    description: Optional[str] = None
    constraints: Optional[FieldConstraints] = None
    ui_config: Optional[Dict[str, Any]] = None
    is_hidden: bool = False
    is_required: bool = False
    default_value: Optional[Any] = None
    is_overridden: bool = False


class TenantEntityView(BaseModel):
    """Complete tenant view of an entity with all overrides applied."""
    entity_type: str
    tenant_id: str
    fields: List[TenantFieldView]
    ui_settings: Optional[Dict[str, Any]] = None
    feature_flags: Optional[Dict[str, bool]] = None
    
    @property
    def visible_fields(self) -> List[TenantFieldView]:
        """Get only visible fields."""
        return [f for f in self.fields if not f.is_hidden]
    
    @property
    def required_fields(self) -> List[TenantFieldView]:
        """Get only required fields."""
        return [f for f in self.fields if f.is_required]
