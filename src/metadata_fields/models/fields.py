"""
Pydantic models for field definitions and related schemas.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator


class FieldScope(str, Enum):
    """Field scope enumeration."""
    CORE = "core"
    TENANT = "tenant"


class FieldType(str, Enum):
    """Field type enumeration."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "datetime"
    JSON = "json"
    ARRAY = "array"


class FieldConstraints(BaseModel):
    """Field validation constraints."""
    required: Optional[bool] = False
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    pattern: Optional[str] = None
    enum_values: Optional[List[str]] = None
    default: Optional[Any] = None


class FieldDefinitionBase(BaseModel):
    """Base field definition model."""
    name: str = Field(..., description="Field name")
    type: FieldType = Field(..., description="Field data type")
    scope: FieldScope = Field(..., description="Field scope (core or tenant)")
    description: Optional[str] = Field(None, description="Field description")
    constraints: Optional[FieldConstraints] = Field(None, description="Field constraints")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI configuration")
    
    @validator('name')
    def validate_name(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('Field name must be alphanumeric with underscores')
        return v.lower()


class FieldDefinitionCreate(FieldDefinitionBase):
    """Model for creating field definitions."""
    tenant_id: Optional[str] = Field(None, description="Tenant ID for tenant-scoped fields")
    
    @validator('tenant_id')
    def validate_tenant_scope(cls, v, values):
        scope = values.get('scope')
        if scope == FieldScope.TENANT and not v:
            raise ValueError('tenant_id is required for tenant-scoped fields')
        if scope == FieldScope.CORE and v:
            raise ValueError('tenant_id must be null for core-scoped fields')
        return v


class FieldDefinitionUpdate(BaseModel):
    """Model for updating field definitions."""
    description: Optional[str] = None
    constraints: Optional[FieldConstraints] = None
    ui_config: Optional[Dict[str, Any]] = None


class FieldDefinition(FieldDefinitionBase):
    """Complete field definition model."""
    id: str = Field(..., description="Field ID")
    entity_type: str = Field(..., description="Entity type")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")
    version_added: str = Field(..., description="Version when field was added")
    version_deprecated: Optional[str] = Field(None, description="Version when field was deprecated")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    is_deprecated: bool = Field(False, description="Whether field is deprecated")
    
    class Config:
        from_attributes = True


class FieldPromotionRequest(BaseModel):
    """Model for promoting tenant field to core."""
    new_version: str = Field(..., description="New version for the promotion")
    migration_strategy: Optional[str] = Field("copy", description="Migration strategy")


class FieldDeprecationRequest(BaseModel):
    """Model for deprecating a field."""
    version_deprecated: str = Field(..., description="Version when field is deprecated")
    reason: Optional[str] = Field(None, description="Reason for deprecation")
    grace_period_days: Optional[int] = Field(90, description="Grace period in days")


class FieldListResponse(BaseModel):
    """Response model for field lists."""
    fields: List[FieldDefinition]
    total: int
    entity_type: str
    tenant_id: Optional[str] = None


class FieldValidationResult(BaseModel):
    """Result of field validation."""
    is_valid: bool
    errors: List[str] = []
    warnings: List[str] = []
