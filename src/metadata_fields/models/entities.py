"""
Pydantic models for entity definitions and schemas.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .fields import FieldDefinition


class EntityTypeBase(BaseModel):
    """Base entity type model."""
    name: str = Field(..., description="Entity type name")
    description: Optional[str] = Field(None, description="Entity description")
    core_fields: List[str] = Field(default_factory=list, description="Core field names")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class EntityTypeCreate(EntityTypeBase):
    """Model for creating entity types."""
    pass


class EntityTypeUpdate(BaseModel):
    """Model for updating entity types."""
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class EntityType(EntityTypeBase):
    """Complete entity type model."""
    id: str = Field(..., description="Entity type ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        """Create from ORM object, mapping meta_data to metadata."""
        data = {
            "id": obj.id,
            "name": obj.name,
            "description": obj.description,
            "core_fields": obj.core_fields,
            "metadata": obj.meta_data,  # Map meta_data to metadata
            "created_at": obj.created_at,
            "updated_at": obj.updated_at
        }
        return cls(**data)


class EntitySchema(BaseModel):
    """Complete entity schema including all fields."""
    entity_type: EntityType
    fields: List[FieldDefinition]
    core_fields: List[FieldDefinition]
    custom_fields: List[FieldDefinition]
    tenant_fields: Optional[List[FieldDefinition]] = None
    schema_version: str
    
    @property
    def field_count(self) -> int:
        """Total number of fields."""
        return len(self.fields)
    
    @property
    def core_field_count(self) -> int:
        """Number of core fields."""
        return len(self.core_fields)
    
    @property
    def custom_field_count(self) -> int:
        """Number of custom fields."""
        return len(self.custom_fields)
    
    @property
    def tenant_field_count(self) -> int:
        """Number of tenant-specific fields."""
        return len(self.tenant_fields) if self.tenant_fields else 0


class EntityListResponse(BaseModel):
    """Response model for entity type lists."""
    entities: List[EntityType]
    total: int


class SchemaVersion(BaseModel):
    """Schema version information."""
    version: str = Field(..., description="Version string")
    description: Optional[str] = Field(None, description="Version description")
    created_at: datetime = Field(..., description="Version creation timestamp")
    changes: List[str] = Field(default_factory=list, description="List of changes")
    
    class Config:
        from_attributes = True


class SchemaVersionListResponse(BaseModel):
    """Response model for schema version lists."""
    versions: List[SchemaVersion]
    current_version: str
    entity_type: str
