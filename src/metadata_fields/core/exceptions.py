"""
Custom exceptions for the metadata fields system.
"""

from typing import Any, Dict, Optional


class MetadataFieldsException(Exception):
    """Base exception for metadata fields system."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class EntityNotFoundError(MetadataFieldsException):
    """Raised when an entity type is not found."""
    pass


class FieldNotFoundError(MetadataFieldsException):
    """Raised when a field is not found."""
    pass


class TenantNotFoundError(MetadataFieldsException):
    """Raised when a tenant is not found."""
    pass


class FieldAlreadyExistsError(MetadataFieldsException):
    """Raised when trying to create a field that already exists."""
    pass


class InvalidFieldScopeError(MetadataFieldsException):
    """Raised when an invalid field scope is provided."""
    pass


class FieldValidationError(MetadataFieldsException):
    """Raised when field validation fails."""
    pass


class TenantPermissionError(MetadataFieldsException):
    """Raised when a tenant doesn't have permission for an operation."""
    pass


class FieldDeprecationError(MetadataFieldsException):
    """Raised when there's an error with field deprecation."""
    pass


class SchemaVersionError(MetadataFieldsException):
    """Raised when there's an error with schema versioning."""
    pass
