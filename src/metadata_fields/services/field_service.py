"""
Service layer for field definition management.
"""

from typing import List, Optional, <PERSON>ple
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from ..database.models import FieldDefinition as FieldDefinitionModel, EntityType as EntityTypeModel
from ..models.fields import (
    FieldDefinition, FieldDefinitionCreate, FieldDefinitionUpdate,
    FieldPromotionRequest, FieldDeprecationRequest, FieldScope
)
from ..core.exceptions import (
    FieldNotFoundError, FieldAlreadyExistsError, EntityNotFoundError,
    InvalidFieldScopeError, TenantPermissionError
)


class FieldService:
    """Service for managing field definitions."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def list_fields(
        self,
        entity_type: str,
        scope: Optional[FieldScope] = None,
        include_deprecated: bool = False,
        tenant_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ) -> <PERSON>ple[List[FieldDefinition], int]:
        """
        List field definitions with filtering.
        
        Args:
            entity_type: Entity type name
            scope: Filter by scope
            include_deprecated: Include deprecated fields
            tenant_id: Tenant ID for filtering
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Search term for field names
            
        Returns:
            Tuple of (field list, total count)
        """
        query = select(FieldDefinitionModel).where(
            FieldDefinitionModel.entity_type == entity_type
        )
        
        # Apply scope filter
        if scope:
            query = query.where(FieldDefinitionModel.scope == scope.value)
        
        # Apply tenant filter
        if tenant_id:
            # Include core fields and tenant-specific fields for this tenant
            query = query.where(
                or_(
                    FieldDefinitionModel.scope == FieldScope.CORE.value,
                    and_(
                        FieldDefinitionModel.scope == FieldScope.TENANT.value,
                        FieldDefinitionModel.tenant_id == tenant_id
                    )
                )
            )
        else:
            # Only core fields if no tenant specified
            query = query.where(FieldDefinitionModel.scope == FieldScope.CORE.value)
        
        # Apply deprecated filter
        if not include_deprecated:
            query = query.where(FieldDefinitionModel.is_deprecated == False)
        
        # Apply search filter
        if search:
            query = query.where(
                or_(
                    FieldDefinitionModel.name.ilike(f"%{search}%"),
                    FieldDefinitionModel.description.ilike(f"%{search}%")
                )
            )
        
        # Get total count
        count_result = await self.db.execute(
            select(FieldDefinitionModel).where(query.whereclause)
        )
        total = len(count_result.fetchall())
        
        # Apply pagination and execute
        query = query.offset(skip).limit(limit).order_by(FieldDefinitionModel.name)
        result = await self.db.execute(query)
        fields = result.scalars().all()
        
        return [FieldDefinition.from_orm(field) for field in fields], total
    
    async def get_field(
        self,
        entity_type: str,
        field_name: str,
        tenant_id: Optional[str] = None
    ) -> Optional[FieldDefinition]:
        """
        Get a specific field definition.
        
        Args:
            entity_type: Entity type name
            field_name: Field name
            tenant_id: Tenant ID for tenant-specific fields
            
        Returns:
            Field definition or None if not found
        """
        query = select(FieldDefinitionModel).where(
            and_(
                FieldDefinitionModel.entity_type == entity_type,
                FieldDefinitionModel.name == field_name
            )
        )
        
        if tenant_id:
            # Try tenant-specific field first, then core field
            tenant_query = query.where(
                and_(
                    FieldDefinitionModel.scope == FieldScope.TENANT.value,
                    FieldDefinitionModel.tenant_id == tenant_id
                )
            )
            result = await self.db.execute(tenant_query)
            field = result.scalar_one_or_none()
            
            if field:
                return FieldDefinition.from_orm(field)
        
        # Try core field
        core_query = query.where(FieldDefinitionModel.scope == FieldScope.CORE.value)
        result = await self.db.execute(core_query)
        field = result.scalar_one_or_none()
        
        return FieldDefinition.from_orm(field) if field else None
    
    async def create_field(
        self,
        entity_type: str,
        field_data: FieldDefinitionCreate
    ) -> FieldDefinition:
        """
        Create a new field definition.
        
        Args:
            entity_type: Entity type name
            field_data: Field creation data
            
        Returns:
            Created field definition
            
        Raises:
            EntityNotFoundError: If entity type doesn't exist
            FieldAlreadyExistsError: If field already exists
        """
        # Check if entity type exists
        entity_result = await self.db.execute(
            select(EntityTypeModel).where(EntityTypeModel.name == entity_type)
        )
        if not entity_result.scalar_one_or_none():
            raise EntityNotFoundError(f"Entity type '{entity_type}' not found")
        
        # Check if field already exists
        existing_field = await self.get_field(entity_type, field_data.name, field_data.tenant_id)
        if existing_field:
            raise FieldAlreadyExistsError(
                f"Field '{field_data.name}' already exists for entity '{entity_type}'"
            )
        
        # Create field definition
        field_model = FieldDefinitionModel(
            name=field_data.name,
            entity_type=entity_type,
            type=field_data.type.value,
            scope=field_data.scope.value,
            description=field_data.description,
            constraints=field_data.constraints.dict() if field_data.constraints else {},
            ui_config=field_data.ui_config or {},
            tenant_id=field_data.tenant_id,
            version_added="1.0.0",  # TODO: Get from version service
            is_deprecated=False
        )
        
        self.db.add(field_model)
        await self.db.commit()
        await self.db.refresh(field_model)
        
        return FieldDefinition.from_orm(field_model)

    async def update_field(
        self,
        entity_type: str,
        field_name: str,
        field_data: FieldDefinitionUpdate,
        tenant_id: Optional[str] = None
    ) -> Optional[FieldDefinition]:
        """
        Update a field definition.

        Args:
            entity_type: Entity type name
            field_name: Field name
            field_data: Field update data
            tenant_id: Tenant ID for tenant-specific fields

        Returns:
            Updated field definition or None if not found
        """
        # Find the field to update
        query = select(FieldDefinitionModel).where(
            and_(
                FieldDefinitionModel.entity_type == entity_type,
                FieldDefinitionModel.name == field_name
            )
        )

        if tenant_id:
            # Try tenant-specific field first
            tenant_query = query.where(
                and_(
                    FieldDefinitionModel.scope == FieldScope.TENANT.value,
                    FieldDefinitionModel.tenant_id == tenant_id
                )
            )
            result = await self.db.execute(tenant_query)
            field = result.scalar_one_or_none()

            if not field:
                # Try core field
                core_query = query.where(FieldDefinitionModel.scope == FieldScope.CORE.value)
                result = await self.db.execute(core_query)
                field = result.scalar_one_or_none()
        else:
            # Only core fields
            core_query = query.where(FieldDefinitionModel.scope == FieldScope.CORE.value)
            result = await self.db.execute(core_query)
            field = result.scalar_one_or_none()

        if not field:
            return None

        # Update field properties
        if field_data.description is not None:
            field.description = field_data.description
        if field_data.constraints is not None:
            field.constraints = field_data.constraints.dict()
        if field_data.ui_config is not None:
            field.ui_config = field_data.ui_config

        await self.db.commit()
        await self.db.refresh(field)

        return FieldDefinition.from_orm(field)

    async def delete_field(
        self,
        entity_type: str,
        field_name: str,
        tenant_id: Optional[str] = None
    ) -> bool:
        """
        Delete a field definition.

        Args:
            entity_type: Entity type name
            field_name: Field name
            tenant_id: Tenant ID for tenant-specific fields

        Returns:
            True if field was deleted, False if not found
        """
        # Find the field to delete
        query = select(FieldDefinitionModel).where(
            and_(
                FieldDefinitionModel.entity_type == entity_type,
                FieldDefinitionModel.name == field_name
            )
        )

        if tenant_id:
            # Only allow deletion of tenant-specific fields by tenant
            query = query.where(
                and_(
                    FieldDefinitionModel.scope == FieldScope.TENANT.value,
                    FieldDefinitionModel.tenant_id == tenant_id
                )
            )
        else:
            # Only core fields
            query = query.where(FieldDefinitionModel.scope == FieldScope.CORE.value)

        result = await self.db.execute(query)
        field = result.scalar_one_or_none()

        if not field:
            return False

        await self.db.delete(field)
        await self.db.commit()

        return True

    async def promote_field(
        self,
        entity_type: str,
        field_name: str,
        promotion_data: FieldPromotionRequest,
        tenant_id: Optional[str] = None
    ) -> Optional[FieldDefinition]:
        """
        Promote a tenant-specific field to core.

        Args:
            entity_type: Entity type name
            field_name: Field name
            promotion_data: Promotion request data
            tenant_id: Tenant ID

        Returns:
            Promoted field definition or None if not found
        """
        if not tenant_id:
            raise TenantPermissionError("Tenant ID required for field promotion")

        # Find the tenant-specific field
        query = select(FieldDefinitionModel).where(
            and_(
                FieldDefinitionModel.entity_type == entity_type,
                FieldDefinitionModel.name == field_name,
                FieldDefinitionModel.scope == FieldScope.TENANT.value,
                FieldDefinitionModel.tenant_id == tenant_id
            )
        )

        result = await self.db.execute(query)
        field = result.scalar_one_or_none()

        if not field:
            return None

        # Check if core field with same name already exists
        core_query = select(FieldDefinitionModel).where(
            and_(
                FieldDefinitionModel.entity_type == entity_type,
                FieldDefinitionModel.name == field_name,
                FieldDefinitionModel.scope == FieldScope.CORE.value
            )
        )

        core_result = await self.db.execute(core_query)
        if core_result.scalar_one_or_none():
            raise FieldAlreadyExistsError(
                f"Core field '{field_name}' already exists for entity '{entity_type}'"
            )

        # Create new core field
        core_field = FieldDefinitionModel(
            name=field.name,
            entity_type=field.entity_type,
            type=field.type,
            scope=FieldScope.CORE.value,
            description=field.description,
            constraints=field.constraints,
            ui_config=field.ui_config,
            tenant_id=None,
            version_added=promotion_data.new_version,
            is_deprecated=False
        )

        self.db.add(core_field)

        # Optionally remove the tenant field or mark it as deprecated
        if promotion_data.migration_strategy == "move":
            await self.db.delete(field)
        else:  # copy strategy - keep tenant field but mark as deprecated
            field.is_deprecated = True
            field.version_deprecated = promotion_data.new_version

        await self.db.commit()
        await self.db.refresh(core_field)

        return FieldDefinition.from_orm(core_field)

    async def deprecate_field(
        self,
        entity_type: str,
        field_name: str,
        deprecation_data: FieldDeprecationRequest,
        tenant_id: Optional[str] = None
    ) -> Optional[FieldDefinition]:
        """
        Deprecate a field.

        Args:
            entity_type: Entity type name
            field_name: Field name
            deprecation_data: Deprecation request data
            tenant_id: Tenant ID for tenant-specific fields

        Returns:
            Deprecated field definition or None if not found
        """
        # Find the field to deprecate
        query = select(FieldDefinitionModel).where(
            and_(
                FieldDefinitionModel.entity_type == entity_type,
                FieldDefinitionModel.name == field_name
            )
        )

        if tenant_id:
            # Try tenant-specific field first
            tenant_query = query.where(
                and_(
                    FieldDefinitionModel.scope == FieldScope.TENANT.value,
                    FieldDefinitionModel.tenant_id == tenant_id
                )
            )
            result = await self.db.execute(tenant_query)
            field = result.scalar_one_or_none()

            if not field:
                # Try core field
                core_query = query.where(FieldDefinitionModel.scope == FieldScope.CORE.value)
                result = await self.db.execute(core_query)
                field = result.scalar_one_or_none()
        else:
            # Only core fields
            core_query = query.where(FieldDefinitionModel.scope == FieldScope.CORE.value)
            result = await self.db.execute(core_query)
            field = result.scalar_one_or_none()

        if not field:
            return None

        # Mark field as deprecated
        field.is_deprecated = True
        field.version_deprecated = deprecation_data.version_deprecated

        await self.db.commit()
        await self.db.refresh(field)

        return FieldDefinition.from_orm(field)
