"""
Service layer for tenant management and configuration.
"""

from typing import List, Optional, Tuple, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_
from sqlalchemy.orm import selectinload

from ..database.models import (
    Tenant as TenantModel,
    TenantConfiguration as TenantConfigurationModel,
    FieldDefinition as FieldDefinitionModel,
    EntityType as EntityTypeModel
)
from ..models.tenants import (
    Tenant, TenantCreate, TenantUpdate,
    TenantConfiguration, TenantConfigurationCreate, TenantConfigurationUpdate,
    TenantEntityView, TenantFieldView, FieldOverride
)
from ..models.fields import FieldDefinition, FieldScope, FieldConstraints
from ..core.exceptions import TenantNotFoundError, EntityNotFoundError


class TenantService:
    """Service for managing tenants and their configurations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def list_tenants(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ) -> <PERSON>ple[List[Tenant], int]:
        """
        List tenants with filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Search term for tenant names
            
        Returns:
            Tuple of (tenant list, total count)
        """
        query = select(TenantModel).where(TenantModel.is_active == True)
        
        # Apply search filter
        if search:
            query = query.where(
                or_(
                    TenantModel.name.ilike(f"%{search}%"),
                    TenantModel.description.ilike(f"%{search}%")
                )
            )
        
        # Get total count
        count_result = await self.db.execute(
            select(TenantModel).where(query.whereclause)
        )
        total = len(count_result.fetchall())
        
        # Apply pagination and execute
        query = query.offset(skip).limit(limit).order_by(TenantModel.name)
        result = await self.db.execute(query)
        tenants = result.scalars().all()
        
        return [Tenant.from_orm(tenant) for tenant in tenants], total
    
    async def get_tenant(self, tenant_id: str) -> Optional[Tenant]:
        """
        Get a specific tenant.
        
        Args:
            tenant_id: Tenant ID
            
        Returns:
            Tenant or None if not found
        """
        result = await self.db.execute(
            select(TenantModel).where(TenantModel.id == tenant_id)
        )
        tenant = result.scalar_one_or_none()
        
        return Tenant.from_orm(tenant) if tenant else None
    
    async def create_tenant(self, tenant_data: TenantCreate) -> Tenant:
        """
        Create a new tenant.
        
        Args:
            tenant_data: Tenant creation data
            
        Returns:
            Created tenant
        """
        tenant_model = TenantModel(
            name=tenant_data.name,
            description=tenant_data.description,
            meta_data=tenant_data.metadata or {},
            is_active=tenant_data.is_active
        )
        
        self.db.add(tenant_model)
        await self.db.commit()
        await self.db.refresh(tenant_model)
        
        return Tenant.from_orm(tenant_model)
    
    async def update_tenant(
        self,
        tenant_id: str,
        tenant_data: TenantUpdate
    ) -> Optional[Tenant]:
        """
        Update a tenant.
        
        Args:
            tenant_id: Tenant ID
            tenant_data: Tenant update data
            
        Returns:
            Updated tenant or None if not found
        """
        result = await self.db.execute(
            select(TenantModel).where(TenantModel.id == tenant_id)
        )
        tenant = result.scalar_one_or_none()
        
        if not tenant:
            return None
        
        # Update tenant properties
        if tenant_data.name is not None:
            tenant.name = tenant_data.name
        if tenant_data.description is not None:
            tenant.description = tenant_data.description
        if tenant_data.metadata is not None:
            tenant.meta_data = tenant_data.metadata
        if tenant_data.is_active is not None:
            tenant.is_active = tenant_data.is_active
        
        await self.db.commit()
        await self.db.refresh(tenant)
        
        return Tenant.from_orm(tenant)
    
    async def delete_tenant(self, tenant_id: str) -> bool:
        """
        Delete a tenant (soft delete by marking as inactive).
        
        Args:
            tenant_id: Tenant ID
            
        Returns:
            True if tenant was deleted, False if not found
        """
        result = await self.db.execute(
            select(TenantModel).where(TenantModel.id == tenant_id)
        )
        tenant = result.scalar_one_or_none()
        
        if not tenant:
            return False
        
        tenant.is_active = False
        await self.db.commit()
        
        return True
    
    async def get_tenant_configuration(
        self,
        tenant_id: str,
        entity_type: str
    ) -> Optional[TenantConfiguration]:
        """
        Get tenant configuration for an entity type.
        
        Args:
            tenant_id: Tenant ID
            entity_type: Entity type name
            
        Returns:
            Tenant configuration or None if not found
        """
        result = await self.db.execute(
            select(TenantConfigurationModel).where(
                and_(
                    TenantConfigurationModel.tenant_id == tenant_id,
                    TenantConfigurationModel.entity_type == entity_type
                )
            )
        )
        config = result.scalar_one_or_none()
        
        return TenantConfiguration.from_orm(config) if config else None
    
    async def create_tenant_configuration(
        self,
        tenant_id: str,
        config_data: TenantConfigurationCreate
    ) -> TenantConfiguration:
        """
        Create a new tenant configuration.
        
        Args:
            tenant_id: Tenant ID
            config_data: Configuration creation data
            
        Returns:
            Created tenant configuration
        """
        config_model = TenantConfigurationModel(
            tenant_id=tenant_id,
            entity_type=config_data.entity_type,
            field_overrides=[override.dict() for override in config_data.field_overrides],
            ui_settings=config_data.ui_settings or {},
            feature_flags=config_data.feature_flags or {}
        )
        
        self.db.add(config_model)
        await self.db.commit()
        await self.db.refresh(config_model)
        
        return TenantConfiguration.from_orm(config_model)

    async def update_tenant_configuration(
        self,
        tenant_id: str,
        entity_type: str,
        config_data: TenantConfigurationUpdate
    ) -> Optional[TenantConfiguration]:
        """
        Update tenant configuration.

        Args:
            tenant_id: Tenant ID
            entity_type: Entity type name
            config_data: Configuration update data

        Returns:
            Updated tenant configuration or None if not found
        """
        result = await self.db.execute(
            select(TenantConfigurationModel).where(
                and_(
                    TenantConfigurationModel.tenant_id == tenant_id,
                    TenantConfigurationModel.entity_type == entity_type
                )
            )
        )
        config = result.scalar_one_or_none()

        if not config:
            return None

        # Update configuration properties
        if config_data.field_overrides is not None:
            config.field_overrides = [override.dict() for override in config_data.field_overrides]
        if config_data.ui_settings is not None:
            config.ui_settings = config_data.ui_settings
        if config_data.feature_flags is not None:
            config.feature_flags = config_data.feature_flags

        await self.db.commit()
        await self.db.refresh(config)

        return TenantConfiguration.from_orm(config)

    async def get_tenant_entity_view(
        self,
        tenant_id: str,
        entity_type: str
    ) -> Optional[TenantEntityView]:
        """
        Get tenant-specific view of an entity with all overrides applied.

        Args:
            tenant_id: Tenant ID
            entity_type: Entity type name

        Returns:
            Tenant entity view with overrides applied or None if not found
        """
        # Check if tenant exists
        tenant_result = await self.db.execute(
            select(TenantModel).where(TenantModel.id == tenant_id)
        )
        if not tenant_result.scalar_one_or_none():
            return None

        # Check if entity type exists
        entity_result = await self.db.execute(
            select(EntityTypeModel).where(EntityTypeModel.name == entity_type)
        )
        if not entity_result.scalar_one_or_none():
            return None

        # Get all fields for this entity (core + tenant-specific)
        fields_query = select(FieldDefinitionModel).where(
            and_(
                FieldDefinitionModel.entity_type == entity_type,
                or_(
                    FieldDefinitionModel.scope == FieldScope.CORE.value,
                    and_(
                        FieldDefinitionModel.scope == FieldScope.TENANT.value,
                        FieldDefinitionModel.tenant_id == tenant_id
                    )
                )
            )
        )

        fields_result = await self.db.execute(fields_query)
        fields = fields_result.scalars().all()

        # Get tenant configuration
        config = await self.get_tenant_configuration(tenant_id, entity_type)

        # Apply overrides to create tenant field views
        field_views = []
        override_map = {}

        if config and config.field_overrides:
            for override_data in config.field_overrides:
                override = FieldOverride(**override_data)
                override_map[override.field_name] = override

        for field in fields:
            override = override_map.get(field.name)

            # Start with base field definition
            constraints = FieldConstraints(**field.constraints) if field.constraints else None

            # Apply overrides
            if override:
                if override.constraints:
                    # Merge constraints
                    base_constraints = constraints.dict() if constraints else {}
                    override_constraints = override.constraints.dict()
                    merged_constraints = {**base_constraints, **override_constraints}
                    constraints = FieldConstraints(**merged_constraints)

                ui_config = override.ui_config or field.ui_config
                is_hidden = override.hidden if override.hidden is not None else False
                is_required = override.required if override.required is not None else (
                    constraints.required if constraints else False
                )
                default_value = override.default_value
                is_overridden = True
            else:
                ui_config = field.ui_config
                is_hidden = False
                is_required = constraints.required if constraints else False
                default_value = constraints.default if constraints else None
                is_overridden = False

            field_view = TenantFieldView(
                field_name=field.name,
                field_type=field.type,
                scope=field.scope,
                description=field.description,
                constraints=constraints,
                ui_config=ui_config,
                is_hidden=is_hidden,
                is_required=is_required,
                default_value=default_value,
                is_overridden=is_overridden
            )

            field_views.append(field_view)

        return TenantEntityView(
            entity_type=entity_type,
            tenant_id=tenant_id,
            fields=field_views,
            ui_settings=config.ui_settings if config else None,
            feature_flags=config.feature_flags if config else None
        )
