"""
Service layer for entity type management.
"""

from typing import List, <PERSON>tional, <PERSON><PERSON>

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_
from sqlalchemy.orm import selectinload

from ..database.models import (
    EntityType as EntityTypeModel,
    FieldDefinition as FieldDefinitionModel,
    SchemaVersion as SchemaVersionModel
)
from ..models.entities import (
    EntityType, EntityTypeCreate, EntityTypeUpdate,
    EntitySchema, SchemaVersion
)
from ..models.fields import FieldDefinition, FieldScope
from ..core.exceptions import EntityNotFoundError, FieldNotFoundError


class EntityService:
    """Service for managing entity types."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def list_entities(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ) -> Tuple[List[EntityType], int]:
        """
        List entity types with filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Search term for entity names
            
        Returns:
            Tuple of (entity list, total count)
        """
        query = select(EntityTypeModel)
        
        # Apply search filter
        if search:
            query = query.where(
                or_(
                    EntityTypeModel.name.ilike(f"%{search}%"),
                    EntityTypeModel.description.ilike(f"%{search}%")
                )
            )
        
        # Get total count
        count_result = await self.db.execute(
            select(EntityTypeModel).where(query.whereclause)
        )
        total = len(count_result.fetchall())
        
        # Apply pagination and execute
        query = query.offset(skip).limit(limit).order_by(EntityTypeModel.name)
        result = await self.db.execute(query)
        entities = result.scalars().all()
        
        return [EntityType.from_orm(entity) for entity in entities], total
    
    async def get_entity(self, entity_type: str) -> Optional[EntityType]:
        """
        Get a specific entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            Entity type or None if not found
        """
        result = await self.db.execute(
            select(EntityTypeModel).where(EntityTypeModel.name == entity_type)
        )
        entity = result.scalar_one_or_none()
        
        return EntityType.from_orm(entity) if entity else None
    
    async def create_entity(self, entity_data: EntityTypeCreate) -> EntityType:
        """
        Create a new entity type.
        
        Args:
            entity_data: Entity creation data
            
        Returns:
            Created entity type
        """
        entity_model = EntityTypeModel(
            name=entity_data.name,
            description=entity_data.description,
            core_fields=entity_data.core_fields,
            meta_data=entity_data.metadata or {}
        )
        
        self.db.add(entity_model)
        await self.db.commit()
        await self.db.refresh(entity_model)
        
        # Create initial schema version
        schema_version = SchemaVersionModel(
            entity_type=entity_model.name,
            version="1.0.0",
            description="Initial version",
            changes=["Entity type created"],
            is_current=True
        )
        
        self.db.add(schema_version)
        await self.db.commit()
        
        return EntityType.from_orm(entity_model)
    
    async def update_entity(
        self,
        entity_type: str,
        entity_data: EntityTypeUpdate
    ) -> Optional[EntityType]:
        """
        Update an entity type.
        
        Args:
            entity_type: Entity type name
            entity_data: Entity update data
            
        Returns:
            Updated entity type or None if not found
        """
        result = await self.db.execute(
            select(EntityTypeModel).where(EntityTypeModel.name == entity_type)
        )
        entity = result.scalar_one_or_none()
        
        if not entity:
            return None
        
        # Update entity properties
        if entity_data.description is not None:
            entity.description = entity_data.description
        if entity_data.metadata is not None:
            entity.meta_data = entity_data.metadata
        
        await self.db.commit()
        await self.db.refresh(entity)
        
        return EntityType.from_orm(entity)
    
    async def delete_entity(self, entity_type: str) -> bool:
        """
        Delete an entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            True if entity was deleted, False if not found
        """
        result = await self.db.execute(
            select(EntityTypeModel).where(EntityTypeModel.name == entity_type)
        )
        entity = result.scalar_one_or_none()
        
        if not entity:
            return False
        
        await self.db.delete(entity)
        await self.db.commit()
        
        return True
    
    async def get_entity_schema(
        self,
        entity_type: str,
        tenant_id: Optional[str] = None
    ) -> Optional[EntitySchema]:
        """
        Get complete entity schema including all fields.
        
        Args:
            entity_type: Entity type name
            tenant_id: Optional tenant ID for tenant-specific view
            
        Returns:
            Complete entity schema or None if not found
        """
        # Get entity type
        entity_result = await self.db.execute(
            select(EntityTypeModel).where(EntityTypeModel.name == entity_type)
        )
        entity = entity_result.scalar_one_or_none()
        
        if not entity:
            return None
        
        # Get all fields for this entity
        fields_query = select(FieldDefinitionModel).where(
            FieldDefinitionModel.entity_type == entity_type
        )
        
        if tenant_id:
            # Include core fields and tenant-specific fields for this tenant
            fields_query = fields_query.where(
                or_(
                    FieldDefinitionModel.scope == FieldScope.CORE.value,
                    and_(
                        FieldDefinitionModel.scope == FieldScope.TENANT.value,
                        FieldDefinitionModel.tenant_id == tenant_id
                    )
                )
            )
        else:
            # Only core fields
            fields_query = fields_query.where(
                FieldDefinitionModel.scope == FieldScope.CORE.value
            )
        
        fields_result = await self.db.execute(fields_query)
        all_fields = fields_result.scalars().all()
        
        # Separate fields by type
        core_fields = [
            FieldDefinition.from_orm(f) for f in all_fields
            if f.scope == FieldScope.CORE.value
        ]
        
        custom_fields = [
            FieldDefinition.from_orm(f) for f in all_fields
            if f.scope == FieldScope.CORE.value  # Custom attributes are core fields in JSONB
        ]
        
        tenant_fields = None
        if tenant_id:
            tenant_fields = [
                FieldDefinition.from_orm(f) for f in all_fields
                if f.scope == FieldScope.TENANT.value and f.tenant_id == tenant_id
            ]
        
        # Get current schema version
        version_result = await self.db.execute(
            select(SchemaVersionModel).where(
                and_(
                    SchemaVersionModel.entity_type == entity_type,
                    SchemaVersionModel.is_current == True
                )
            )
        )
        current_version = version_result.scalar_one_or_none()
        schema_version = current_version.version if current_version else "1.0.0"
        
        return EntitySchema(
            entity_type=EntityType.from_orm(entity),
            fields=[FieldDefinition.from_orm(f) for f in all_fields],
            core_fields=core_fields,
            custom_fields=custom_fields,
            tenant_fields=tenant_fields,
            schema_version=schema_version
        )
    
    async def list_schema_versions(
        self,
        entity_type: str
    ) -> Tuple[List[SchemaVersion], str]:
        """
        List all schema versions for an entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            Tuple of (version list, current version)
        """
        # Get all versions
        versions_result = await self.db.execute(
            select(SchemaVersionModel)
            .where(SchemaVersionModel.entity_type == entity_type)
            .order_by(SchemaVersionModel.created_at.desc())
        )
        versions = versions_result.scalars().all()
        
        # Find current version
        current_version = "1.0.0"
        for version in versions:
            if version.is_current:
                current_version = version.version
                break
        
        return [SchemaVersion.from_orm(v) for v in versions], current_version
