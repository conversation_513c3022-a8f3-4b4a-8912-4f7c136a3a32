# Quick Start Guide - Metadata Fields API

## 🚀 Automated Setup (Recommended)

```bash
# Run the automated setup script
./setup.sh
```

This will handle everything automatically. If it works, skip to the "Testing" section below.

## 🛠️ Manual Setup (If automated setup fails)

### Step 1: Create Virtual Environment

```bash
# Create virtual environment
python3 -m venv venv

# Activate it
source venv/bin/activate  # On macOS/Linux
# OR
venv\Scripts\activate     # On Windows
```

### Step 2: Install Dependencies

```bash
# Upgrade pip
pip install --upgrade pip

# Install requirements
pip install -r requirements.txt

# Install package in development mode
pip install -e .
```

### Step 3: Set Up PostgreSQL

```bash
# Create database
createdb metadata_fields

# Create user (optional)
psql -c "CREATE USER metadata_user WITH PASSWORD 'metadata_pass';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE metadata_fields TO metadata_user;"
```

### Step 4: Configure Environment

```bash
# Copy environment file
cp .env.example .env

# Edit .env file with your database settings
# Update DATABASE_URL line to:
# DATABASE_URL=postgresql+asyncpg://metadata_user:metadata_pass@localhost:5432/metadata_fields
```

### Step 5: Set Up Database Tables

```bash
# Create migration directory
mkdir -p src/metadata_fields/database/migrations/versions

# Create initial migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

### Step 6: Create Sample Data (Optional)

```bash
python scripts/setup_database.py
```

## 🧪 Testing the Setup

### Start the Server

```bash
# Option 1: Using the startup script
python scripts/start_server.py

# Option 2: Using uvicorn directly
uvicorn src.metadata_fields.main:app --reload

# Option 3: Using make
make run
```

### Verify It's Working

```bash
# Test health endpoint
curl http://localhost:8000/health

# Should return: {"status": "healthy"}
```

### View API Documentation

Open in your browser:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Run API Tests

```bash
# Make the test script executable
chmod +x scripts/test_api.sh

# Run comprehensive API tests
./scripts/test_api.sh

# Or using make
make test-api
```

### Run the Demo

```bash
# In another terminal (keep server running)
python scripts/run_demo.py

# Or using make
make demo
```

## 🎯 Quick API Examples

### 1. Create an Entity

```bash
curl -X POST "http://localhost:8000/api/v1/entities/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Address",
    "description": "Address entity for testing",
    "core_fields": ["name", "address1", "city", "zipcode"]
  }'
```

### 2. Create a Tenant

```bash
curl -X POST "http://localhost:8000/api/v1/tenants/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Company",
    "description": "A test company"
  }'
```

### 3. Add a Core Field

```bash
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "address2",
    "type": "string",
    "scope": "core",
    "description": "Secondary address line",
    "constraints": {"required": false, "max_length": 100}
  }'
```

### 4. Add a Tenant-Specific Field

```bash
# First get the tenant ID
TENANT_ID=$(curl -s "http://localhost:8000/api/v1/tenants/" | jq -r '.[0].id')

# Add tenant-specific field
curl -X POST "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -d '{
    "name": "building_number",
    "type": "string",
    "scope": "tenant",
    "description": "Building number",
    "constraints": {"required": true, "max_length": 10}
  }'
```

### 5. List Fields for a Tenant

```bash
curl -X GET "http://localhost:8000/api/v1/entities/Address/fields" \
  -H "X-Tenant-ID: $TENANT_ID"
```

## 🔧 Troubleshooting

### Database Connection Issues

```bash
# Check if PostgreSQL is running
pg_isready

# Check if database exists
psql -l | grep metadata_fields

# Test connection
psql -d metadata_fields -c "SELECT version();"
```

### Virtual Environment Issues

```bash
# Check if in virtual environment
which python
# Should show path with 'venv' in it

# Reactivate if needed
source venv/bin/activate
```

### Import Errors

```bash
# Reinstall package
pip install -e .

# Check installation
python -c "import src.metadata_fields; print('OK')"
```

### Port Already in Use

```bash
# Find process using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>
```

## 📚 Next Steps

1. **Explore the API**: Use the Swagger UI at http://localhost:8000/docs
2. **Read the Documentation**: Check out `API_DOCUMENTATION.md`
3. **Run Tests**: Execute `make test` to run the test suite
4. **Customize**: Modify the code to fit your specific needs

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Server starts without errors
- ✅ Health check returns `{"status": "healthy"}`
- ✅ API docs load at http://localhost:8000/docs
- ✅ Test script passes all checks
- ✅ Demo runs successfully

Happy coding! 🚀
