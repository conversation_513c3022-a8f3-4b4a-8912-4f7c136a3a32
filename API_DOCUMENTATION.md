# Metadata Fields API Documentation

## Overview

The Metadata Fields API provides a flexible field definition system for multi-tenant SaaS applications. It supports dynamic field management with tenant isolation, field lifecycle management, and metadata-driven configuration.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Currently, the API uses tenant identification via headers. For production use, implement proper authentication.

### Headers

- `X-Tenant-ID`: Required for tenant-specific operations

## Core Endpoints

### Entity Management

#### List Entities
```http
GET /entities/
```

**Query Parameters:**
- `skip` (int): Number of records to skip (default: 0)
- `limit` (int): Maximum records to return (default: 100, max: 1000)
- `search` (string): Search term for entity names/descriptions

**Response:**
```json
{
  "entities": [
    {
      "id": "uuid",
      "name": "Address",
      "description": "Address entity",
      "core_fields": ["name", "address1", "city"],
      "metadata": {},
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1
}
```

#### Create Entity
```http
POST /entities/
```

**Request Body:**
```json
{
  "name": "Address",
  "description": "Address entity for multi-tenant SaaS",
  "core_fields": ["name", "address1", "city", "zipcode"],
  "metadata": {"version": "1.0.0"}
}
```

#### Get Entity
```http
GET /entities/{entity_type}
```

#### Update Entity
```http
PUT /entities/{entity_type}
```

#### Delete Entity
```http
DELETE /entities/{entity_type}
```

#### Get Entity Schema
```http
GET /entities/{entity_type}/schema
```

**Headers:**
- `X-Tenant-ID` (optional): For tenant-specific view

**Response:**
```json
{
  "entity_type": {
    "id": "uuid",
    "name": "Address",
    "description": "Address entity"
  },
  "fields": [...],
  "core_fields": [...],
  "custom_fields": [...],
  "tenant_fields": [...],
  "schema_version": "1.2.0"
}
```

### Field Management

#### List Fields
```http
GET /entities/{entity_type}/fields
```

**Query Parameters:**
- `scope` (enum): Filter by scope (`core`, `tenant`, `all`)
- `include_deprecated` (bool): Include deprecated fields (default: false)
- `skip`, `limit`, `search`: Standard pagination parameters

**Headers:**
- `X-Tenant-ID` (optional): For tenant-specific view

**Response:**
```json
{
  "fields": [
    {
      "id": "uuid",
      "name": "address2",
      "entity_type": "Address",
      "type": "string",
      "scope": "core",
      "description": "Secondary address line",
      "constraints": {
        "required": false,
        "max_length": 100
      },
      "ui_config": {},
      "tenant_id": null,
      "version_added": "1.2.0",
      "version_deprecated": null,
      "is_deprecated": false,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "entity_type": "Address",
  "tenant_id": null
}
```

#### Get Field
```http
GET /entities/{entity_type}/fields/{field_name}
```

#### Create Field
```http
POST /entities/{entity_type}/fields
```

**Request Body:**
```json
{
  "name": "building_number",
  "type": "string",
  "scope": "tenant",
  "description": "Building number",
  "constraints": {
    "required": true,
    "max_length": 10,
    "pattern": "^[A-Z0-9]+$"
  },
  "ui_config": {
    "label": "Building #",
    "placeholder": "Enter building number"
  }
}
```

**Field Types:**
- `string`, `integer`, `float`, `boolean`
- `date`, `datetime`, `json`, `array`

**Field Scopes:**
- `core`: Available to all tenants
- `tenant`: Specific to one tenant

#### Update Field
```http
PUT /entities/{entity_type}/fields/{field_name}
```

#### Delete Field
```http
DELETE /entities/{entity_type}/fields/{field_name}
```

### Field Lifecycle

#### Promote Field to Core
```http
POST /entities/{entity_type}/fields/{field_name}/promote
```

**Headers:**
- `X-Tenant-ID` (required): Tenant that owns the field

**Request Body:**
```json
{
  "new_version": "1.3.0",
  "migration_strategy": "copy"
}
```

**Migration Strategies:**
- `copy`: Keep tenant field, create new core field
- `move`: Remove tenant field, create core field

#### Deprecate Field
```http
POST /entities/{entity_type}/fields/{field_name}/deprecate
```

**Request Body:**
```json
{
  "version_deprecated": "1.4.0",
  "reason": "Field is no longer needed",
  "grace_period_days": 90
}
```

### Tenant Management

#### List Tenants
```http
GET /tenants/
```

#### Create Tenant
```http
POST /tenants/
```

**Request Body:**
```json
{
  "name": "ACME Corp",
  "description": "Manufacturing company",
  "metadata": {"industry": "manufacturing"},
  "is_active": true
}
```

#### Get Tenant
```http
GET /tenants/{tenant_id}
```

#### Update Tenant
```http
PUT /tenants/{tenant_id}
```

#### Delete Tenant
```http
DELETE /tenants/{tenant_id}
```

### Tenant Configuration

#### Get Tenant Configuration
```http
GET /tenants/{tenant_id}/entities/{entity_type}/config
```

#### Update Tenant Configuration
```http
PUT /tenants/{tenant_id}/entities/{entity_type}/config
```

**Request Body:**
```json
{
  "field_overrides": [
    {
      "field_name": "zipcode",
      "constraints": {
        "required": true,
        "pattern": "^[0-9]{5}$"
      },
      "ui_config": {
        "label": "ZIP Code"
      },
      "hidden": false,
      "required": true
    }
  ],
  "ui_settings": {
    "field_order": ["name", "address1", "address2", "city", "zipcode"],
    "theme": "compact"
  },
  "feature_flags": {
    "enable_address_validation": true
  }
}
```

#### Get Tenant Entity View
```http
GET /tenants/{tenant_id}/entities/{entity_type}/fields
```

**Response:**
```json
{
  "entity_type": "Address",
  "tenant_id": "tenant-uuid",
  "fields": [
    {
      "field_name": "zipcode",
      "field_type": "string",
      "scope": "core",
      "description": "ZIP code",
      "constraints": {
        "required": true,
        "pattern": "^[0-9]{5}$"
      },
      "ui_config": {
        "label": "ZIP Code"
      },
      "is_hidden": false,
      "is_required": true,
      "default_value": null,
      "is_overridden": true
    }
  ],
  "ui_settings": {...},
  "feature_flags": {...}
}
```

## Error Responses

All endpoints return standard HTTP status codes:

- `200`: Success
- `201`: Created
- `204`: No Content
- `400`: Bad Request
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

**Error Response Format:**
```json
{
  "detail": "Error message",
  "error_code": "FIELD_NOT_FOUND",
  "details": {}
}
```

## Field Constraints

### String Fields
```json
{
  "required": true,
  "min_length": 1,
  "max_length": 100,
  "pattern": "^[A-Za-z0-9]+$",
  "enum_values": ["option1", "option2"],
  "default": "default_value"
}
```

### Numeric Fields
```json
{
  "required": true,
  "min_value": 0,
  "max_value": 100,
  "default": 0
}
```

### Boolean Fields
```json
{
  "required": false,
  "default": false
}
```

## UI Configuration

Fields can include UI configuration for frontend rendering:

```json
{
  "ui_config": {
    "label": "Display Label",
    "placeholder": "Enter value...",
    "help_text": "Additional help text",
    "widget": "text|textarea|select|checkbox|radio",
    "order": 10,
    "group": "address_info",
    "validation_message": "Custom validation message"
  }
}
```

## Versioning

The API uses semantic versioning for schema changes:

- **Major**: Breaking changes
- **Minor**: New features, field additions
- **Patch**: Bug fixes, constraint updates

Version information is tracked in:
- Field definitions (`version_added`, `version_deprecated`)
- Schema versions per entity type
- Tenant configuration changes
