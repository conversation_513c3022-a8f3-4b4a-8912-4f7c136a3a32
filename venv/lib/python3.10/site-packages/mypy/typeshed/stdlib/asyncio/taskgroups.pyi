import sys
from contextvars import Context
from types import <PERSON>backType
from typing import Any, TypeVar
from typing_extensions import Self

from . import _CoroutineLike
from .events import Abstract<PERSON><PERSON><PERSON>oop
from .tasks import Task

# Keep asyncio.__all__ updated with any changes to __all__ here
if sys.version_info >= (3, 12):
    __all__ = ("TaskGroup",)
else:
    __all__ = ["TaskGroup"]

_T = TypeVar("_T")

class TaskGroup:
    _loop: AbstractEventLoop | None
    _tasks: set[Task[Any]]

    async def __aenter__(self) -> Self: ...
    async def __aexit__(self, et: type[BaseException] | None, exc: BaseException | None, tb: TracebackType | None) -> None: ...
    def create_task(self, coro: _CoroutineLike[_T], *, name: str | None = None, context: Context | None = None) -> Task[_T]: ...
    def _on_task_done(self, task: Task[object]) -> None: ...
