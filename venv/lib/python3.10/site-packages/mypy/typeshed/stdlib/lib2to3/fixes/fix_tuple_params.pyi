from _typeshed import Incomplete
from typing import ClassVar, Literal

from .. import fixer_base

def is_docstring(stmt): ...

class FixTupleParams(fixer_base.BaseFix):
    BM_compatible: ClassVar[Literal[True]]
    PATTERN: ClassVar[str]
    def transform(self, node, results): ...
    def transform_lambda(self, node, results) -> None: ...

def simplify_args(node): ...
def find_params(node): ...
def map_to_index(param_list, prefix=..., d: Incomplete | None = ...): ...
def tuple_name(param_list): ...
