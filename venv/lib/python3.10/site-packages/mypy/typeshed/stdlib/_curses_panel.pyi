from _curses import window
from typing import final

__version__: str
version: str

class error(Exception): ...

@final
class panel:
    def above(self) -> panel: ...
    def below(self) -> panel: ...
    def bottom(self) -> None: ...
    def hidden(self) -> bool: ...
    def hide(self) -> None: ...
    def move(self, y: int, x: int, /) -> None: ...
    def replace(self, win: window, /) -> None: ...
    def set_userptr(self, obj: object, /) -> None: ...
    def show(self) -> None: ...
    def top(self) -> None: ...
    def userptr(self) -> object: ...
    def window(self) -> window: ...

def bottom_panel() -> panel: ...
def new_panel(win: window, /) -> panel: ...
def top_panel() -> panel: ...
def update_panels() -> panel: ...
