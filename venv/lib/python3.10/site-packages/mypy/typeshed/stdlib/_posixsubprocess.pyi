import sys
from _typeshed import StrOrBytesPath
from collections.abc import Callable, Sequence
from typing import SupportsIndex

if sys.platform != "win32":
    if sys.version_info >= (3, 14):
        def fork_exec(
            args: Sequence[StrOrBytesPath] | None,
            executable_list: Sequence[bytes],
            close_fds: bool,
            pass_fds: tuple[int, ...],
            cwd: str,
            env: Sequence[bytes] | None,
            p2cread: int,
            p2cwrite: int,
            c2pread: int,
            c2pwrite: int,
            errread: int,
            errwrite: int,
            errpipe_read: int,
            errpipe_write: int,
            restore_signals: int,
            call_setsid: int,
            pgid_to_set: int,
            gid: SupportsIndex | None,
            extra_groups: list[int] | None,
            uid: SupportsIndex | None,
            child_umask: int,
            preexec_fn: Callable[[], None],
            /,
        ) -> int: ...
    else:
        def fork_exec(
            args: Sequence[StrOrBytesPath] | None,
            executable_list: Sequence[bytes],
            close_fds: bool,
            pass_fds: tuple[int, ...],
            cwd: str,
            env: Sequence[bytes] | None,
            p2cread: int,
            p2cwrite: int,
            c2pread: int,
            c2pwrite: int,
            errread: int,
            errwrite: int,
            errpipe_read: int,
            errpipe_write: int,
            restore_signals: bool,
            call_setsid: bool,
            pgid_to_set: int,
            gid: SupportsIndex | None,
            extra_groups: list[int] | None,
            uid: SupportsIndex | None,
            child_umask: int,
            preexec_fn: Callable[[], None],
            allow_vfork: bool,
            /,
        ) -> int: ...
