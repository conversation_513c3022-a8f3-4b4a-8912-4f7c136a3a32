from typing_extensions import TypeAlias

# Actually Tuple[(int,) * 625]
_State: TypeAlias = tuple[int, ...]

class Random:
    def __init__(self, seed: object = ...) -> None: ...
    def seed(self, n: object = None, /) -> None: ...
    def getstate(self) -> _State: ...
    def setstate(self, state: _State, /) -> None: ...
    def random(self) -> float: ...
    def getrandbits(self, k: int, /) -> int: ...
