from typing import <PERSON><PERSON><PERSON><PERSON>
from xml.sax.xmlreader import Locator

class SAXException(Exception):
    def __init__(self, msg: str, exception: Exception | None = None) -> None: ...
    def getMessage(self) -> str: ...
    def getException(self) -> Exception | None: ...
    def __getitem__(self, ix: object) -> NoReturn: ...

class SAXParseException(SAXException):
    def __init__(self, msg: str, exception: Exception | None, locator: Locator) -> None: ...
    def getColumnNumber(self) -> int | None: ...
    def getLineNumber(self) -> int | None: ...
    def getPublicId(self) -> str | None: ...
    def getSystemId(self) -> str | None: ...

class SAXNotRecognizedException(SAXException): ...
class SAXNotSupportedException(SAXException): ...
class SAXReaderNotAvailable(SAXNotSupportedException): ...
