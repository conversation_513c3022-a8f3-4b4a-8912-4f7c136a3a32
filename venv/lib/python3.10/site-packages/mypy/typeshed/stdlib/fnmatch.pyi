import sys
from collections.abc import Iterable
from typing import AnyStr

__all__ = ["filter", "fnmatch", "fnmatchcase", "translate"]
if sys.version_info >= (3, 14):
    __all__ += ["filterfalse"]

def fnmatch(name: AnyStr, pat: AnyStr) -> bool: ...
def fnmatchcase(name: AnyStr, pat: AnyStr) -> bool: ...
def filter(names: Iterable[AnyStr], pat: AnyStr) -> list[AnyStr]: ...
def translate(pat: str) -> str: ...

if sys.version_info >= (3, 14):
    def filterfalse(names: Iterable[AnyStr], pat: AnyStr) -> list[AnyStr]: ...
