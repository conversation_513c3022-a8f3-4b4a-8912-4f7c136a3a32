"""
Basic API tests for the metadata fields system.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from src.metadata_fields.main import app
from src.metadata_fields.database.connection import get_db, Base


# Test database URL (use in-memory SQLite for testing)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine and session
test_engine = create_async_engine(TEST_DATABASE_URL, echo=True)
TestSessionLocal = sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


async def override_get_db():
    """Override database dependency for testing."""
    async with TestSessionLocal() as session:
        yield session


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture
async def setup_database():
    """Set up test database."""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


def test_root_endpoint(client):
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "name" in data
    assert "version" in data


def test_health_check(client):
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


@pytest.mark.asyncio
async def test_list_entities_empty(client, setup_database):
    """Test listing entities when none exist."""
    response = client.get("/api/v1/entities/")
    assert response.status_code == 200
    data = response.json()
    assert data["entities"] == []
    assert data["total"] == 0


@pytest.mark.asyncio
async def test_create_entity(client, setup_database):
    """Test creating a new entity type."""
    entity_data = {
        "name": "Address",
        "description": "Address entity for testing",
        "core_fields": ["name", "address1", "city", "zipcode"],
        "metadata": {"version": "1.0.0"}
    }
    
    response = client.post("/api/v1/entities/", json=entity_data)
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "Address"
    assert data["description"] == "Address entity for testing"
    assert data["core_fields"] == ["name", "address1", "city", "zipcode"]


@pytest.mark.asyncio
async def test_get_entity_not_found(client, setup_database):
    """Test getting a non-existent entity."""
    response = client.get("/api/v1/entities/NonExistent")
    assert response.status_code == 404
    data = response.json()
    assert "not found" in data["detail"].lower()


@pytest.mark.asyncio
async def test_list_fields_empty(client, setup_database):
    """Test listing fields for non-existent entity."""
    response = client.get("/api/v1/entities/Address/fields")
    assert response.status_code == 200
    data = response.json()
    assert data["fields"] == []
    assert data["total"] == 0
    assert data["entity_type"] == "Address"


def test_api_documentation():
    """Test that API documentation is accessible."""
    client = TestClient(app)
    
    # Test Swagger UI
    response = client.get("/docs")
    assert response.status_code == 200
    
    # Test ReDoc
    response = client.get("/redoc")
    assert response.status_code == 200
    
    # Test OpenAPI schema
    response = client.get("/openapi.json")
    assert response.status_code == 200
    schema = response.json()
    assert "openapi" in schema
    assert "info" in schema
