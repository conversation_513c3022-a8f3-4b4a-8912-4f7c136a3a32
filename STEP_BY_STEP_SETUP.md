# Step-by-Step Setup Guide

## 🚀 Complete Setup Instructions

Follow these steps exactly to get the Metadata Fields API running:

### Step 1: Create Virtual Environment

```bash
# Make sure you're in the project directory
cd /path/to/metadata_fields

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# You should see (venv) in your prompt
```

### Step 2: Install Dependencies

```bash
# Make sure virtual environment is activated (you should see (venv) in prompt)
# Upgrade pip first
pip install --upgrade pip

# Install all dependencies
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

### Step 3: Verify Installation

```bash
# Test that imports work
python test_setup.py

# Should show "Setup verification passed!"
```

### Step 4: Set Up Environment File

```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file if needed (optional for local development)
# The default settings should work with a local PostgreSQL installation
```

### Step 5: Set Up PostgreSQL Database

```bash
# Create the database (make sure PostgreSQL is running)
createdb metadata_fields

# Optional: Create a dedicated user
psql -c "CREATE USER metadata_user WITH PASSWORD 'metadata_pass';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE metadata_fields TO metadata_user;"

# Test connection
psql -d metadata_fields -c "SELECT version();"
```

### Step 6: Set Up Database Tables and Sample Data

```bash
# Make sure virtual environment is still activated
python scripts/simple_setup.py

# Should show "Database setup completed successfully!"
```

### Step 7: Start the Server

```bash
# Start the API server
python scripts/start_server.py

# You should see:
# 🚀 Starting Metadata Fields API Server
# Server will be available at: http://localhost:8000
```

### Step 8: Test the API

Open a new terminal (keep the server running) and run:

```bash
# Activate virtual environment in the new terminal
cd /path/to/metadata_fields
source venv/bin/activate

# Test the health endpoint
curl http://localhost:8000/health

# Should return: {"status":"healthy"}

# Run comprehensive API tests
./scripts/test_api.sh

# Should show all tests passing
```

### Step 9: Explore the API

Open your browser and visit:

- **API Documentation**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## 🧪 Quick API Examples

### List Entities
```bash
curl http://localhost:8000/api/v1/entities/
```

### List Tenants
```bash
curl http://localhost:8000/api/v1/tenants/
```

### Get Address Fields
```bash
curl http://localhost:8000/api/v1/entities/Address/fields
```

### Get Tenant-Specific View
```bash
# Get tenant ID first
TENANT_ID=$(curl -s http://localhost:8000/api/v1/tenants/ | jq -r '.[0].id')

# Get tenant-specific field view
curl -H "X-Tenant-ID: $TENANT_ID" http://localhost:8000/api/v1/entities/Address/fields
```

## 🔧 Troubleshooting

### Virtual Environment Issues

```bash
# Check if virtual environment is activated
which python
# Should show path with 'venv' in it

# If not activated, run:
source venv/bin/activate
```

### Import Errors

```bash
# Reinstall dependencies
pip install -r requirements.txt
pip install -e .

# Test imports
python -c "import src.metadata_fields; print('OK')"
```

### Database Connection Issues

```bash
# Check if PostgreSQL is running
pg_isready

# Check if database exists
psql -l | grep metadata_fields

# Recreate database if needed
dropdb metadata_fields
createdb metadata_fields
python scripts/simple_setup.py
```

### Port Already in Use

```bash
# Find process using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>
```

### Server Won't Start

```bash
# Check for errors in the startup
python scripts/start_server.py

# If you see import errors, make sure virtual environment is activated
source venv/bin/activate
```

## ✅ Success Checklist

You know everything is working when:

- [ ] Virtual environment is activated (see `(venv)` in prompt)
- [ ] `python test_setup.py` passes
- [ ] `python scripts/simple_setup.py` completes successfully
- [ ] `python scripts/start_server.py` starts without errors
- [ ] `curl http://localhost:8000/health` returns `{"status":"healthy"}`
- [ ] API docs load at http://localhost:8000/docs
- [ ] `./scripts/test_api.sh` passes all tests

## 🎯 Common Commands

```bash
# Activate virtual environment
source venv/bin/activate

# Set up database
python scripts/simple_setup.py

# Start server
python scripts/start_server.py

# Test API
./scripts/test_api.sh

# Run demo
python scripts/run_demo.py
```

## 🎉 Next Steps

Once everything is working:

1. **Explore the API**: Use the Swagger UI at http://localhost:8000/docs
2. **Read the Documentation**: Check out `API_DOCUMENTATION.md`
3. **Run the Demo**: Execute `python scripts/run_demo.py`
4. **Customize**: Modify the code to fit your specific needs

Happy coding! 🚀
