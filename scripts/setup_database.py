#!/usr/bin/env python3
"""
<PERSON>ript to set up the database with initial data for testing.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from sqlalchemy.ext.asyncio import AsyncSession
from src.metadata_fields.database.connection import AsyncSessionLocal, create_tables
from src.metadata_fields.database.models import (
    EntityType, Tenant, FieldDefinition, TenantConfiguration, SchemaVersion
)


async def create_sample_data():
    """Create sample data for testing."""
    async with AsyncSessionLocal() as session:
        try:
            print("🔧 Creating sample data...")
            
            # Create Address entity
            address_entity = EntityType(
                name="Address",
                description="Address entity for multi-tenant SaaS",
                core_fields=["name", "address1", "city", "zipcode"],
                meta_data={"version": "1.0.0", "created_by": "setup_script"}
            )
            session.add(address_entity)
            
            # Create Product entity
            product_entity = EntityType(
                name="Product",
                description="Product catalog entity",
                core_fields=["name", "price", "description", "sku"],
                meta_data={"version": "1.0.0", "created_by": "setup_script"}
            )
            session.add(product_entity)
            
            # Create Customer entity
            customer_entity = EntityType(
                name="Customer",
                description="Customer management entity",
                core_fields=["first_name", "last_name", "email", "phone"],
                meta_data={"version": "1.0.0", "created_by": "setup_script"}
            )
            session.add(customer_entity)
            
            # Create sample tenants
            tenant_acme = Tenant(
                name="ACME Corp",
                description="Manufacturing company",
                meta_data={"industry": "manufacturing", "size": "large"},
                is_active=True
            )
            session.add(tenant_acme)
            
            tenant_tech = Tenant(
                name="TechStart Inc",
                description="Technology startup",
                meta_data={"industry": "technology", "size": "small"},
                is_active=True
            )
            session.add(tenant_tech)
            
            # Commit to get IDs
            await session.commit()
            await session.refresh(tenant_acme)
            await session.refresh(tenant_tech)
            
            # Create some core fields for Address
            address2_field = FieldDefinition(
                name="address2",
                entity_type="Address",
                type="string",
                scope="core",
                description="Secondary address line",
                constraints={
                    "required": False,
                    "max_length": 100
                },
                ui_config={
                    "label": "Address Line 2",
                    "placeholder": "Apt, Suite, Unit, etc."
                },
                version_added="1.1.0",
                is_deprecated=False
            )
            session.add(address2_field)
            
            country_field = FieldDefinition(
                name="country",
                entity_type="Address",
                type="string",
                scope="core",
                description="Country code",
                constraints={
                    "required": True,
                    "max_length": 3,
                    "pattern": "^[A-Z]{2,3}$",
                    "default": "USA"
                },
                ui_config={
                    "label": "Country",
                    "help_text": "ISO country code"
                },
                version_added="1.2.0",
                is_deprecated=False
            )
            session.add(country_field)
            
            # Create tenant-specific fields
            building_field = FieldDefinition(
                name="building_number",
                entity_type="Address",
                type="string",
                scope="tenant",
                description="Building number for ACME Corp addresses",
                constraints={
                    "required": True,
                    "max_length": 10,
                    "pattern": "^[A-Z0-9]+$"
                },
                ui_config={
                    "label": "Building #",
                    "placeholder": "e.g., B123"
                },
                tenant_id=tenant_acme.id,
                version_added="1.0.0",
                is_deprecated=False
            )
            session.add(building_field)
            
            district_field = FieldDefinition(
                name="district",
                entity_type="Address",
                type="string",
                scope="tenant",
                description="District information for TechStart addresses",
                constraints={
                    "required": False,
                    "max_length": 50
                },
                ui_config={
                    "label": "District",
                    "placeholder": "Enter district name"
                },
                tenant_id=tenant_tech.id,
                version_added="1.0.0",
                is_deprecated=False
            )
            session.add(district_field)
            
            # Create schema versions
            address_v1 = SchemaVersion(
                entity_type="Address",
                version="1.0.0",
                description="Initial Address entity",
                changes=["Created Address entity with core fields"],
                is_current=False
            )
            session.add(address_v1)
            
            address_v11 = SchemaVersion(
                entity_type="Address",
                version="1.1.0",
                description="Added address2 field",
                changes=["Added address2 core field"],
                is_current=False
            )
            session.add(address_v11)
            
            address_v12 = SchemaVersion(
                entity_type="Address",
                version="1.2.0",
                description="Added country field",
                changes=["Added country core field with validation"],
                is_current=True
            )
            session.add(address_v12)
            
            # Create tenant configuration for ACME Corp
            acme_config = TenantConfiguration(
                tenant_id=tenant_acme.id,
                entity_type="Address",
                field_overrides=[
                    {
                        "field_name": "zipcode",
                        "constraints": {
                            "required": True,
                            "pattern": "^[0-9]{5}(-[0-9]{4})?$"
                        },
                        "ui_config": {
                            "label": "ZIP Code",
                            "help_text": "5-digit ZIP or ZIP+4 format"
                        },
                        "required": True
                    },
                    {
                        "field_name": "country",
                        "default_value": "USA",
                        "hidden": True
                    }
                ],
                ui_settings={
                    "field_order": ["name", "address1", "address2", "building_number", "city", "zipcode"],
                    "theme": "compact",
                    "show_labels": True
                },
                feature_flags={
                    "enable_address_validation": True,
                    "require_building_number": True,
                    "auto_format_zipcode": True
                }
            )
            session.add(acme_config)
            
            await session.commit()
            print("✅ Sample data created successfully!")
            
            # Print summary
            print("\n📊 Created:")
            print(f"  • 3 Entity Types: Address, Product, Customer")
            print(f"  • 2 Tenants: ACME Corp, TechStart Inc")
            print(f"  • 4 Field Definitions (2 core, 2 tenant-specific)")
            print(f"  • 3 Schema Versions for Address entity")
            print(f"  • 1 Tenant Configuration with overrides")
            
        except Exception as e:
            print(f"❌ Error creating sample data: {e}")
            await session.rollback()
            raise


async def main():
    """Main setup function."""
    print("🚀 Setting up Metadata Fields Database")
    print("=" * 50)
    
    try:
        # Create tables using the direct approach
        print("🔧 Creating database tables...")
        from src.metadata_fields.database.models import Base
        from src.metadata_fields.core.config import get_settings
        from sqlalchemy.ext.asyncio import create_async_engine

        settings = get_settings()
        engine = create_async_engine(settings.DATABASE_URL, echo=False)

        async with engine.begin() as conn:
            # Drop all tables first (for clean setup)
            await conn.run_sync(Base.metadata.drop_all)
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)

        await engine.dispose()
        print("✅ Database tables created!")
        
        # Create sample data
        await create_sample_data()
        
        print("\n🎉 Database setup completed successfully!")
        print("=" * 50)
        print("You can now start the server with:")
        print("  python scripts/start_server.py")
        print("\nOr run the demo with:")
        print("  python scripts/run_demo.py")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
