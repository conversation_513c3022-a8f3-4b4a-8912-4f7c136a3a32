#!/bin/bash

# API Testing Script for Metadata Fields
# This script tests all the major API endpoints

set -e  # Exit on any error

BASE_URL="http://localhost:8000"
API_BASE="$BASE_URL/api/v1"

echo "🧪 Testing Metadata Fields API"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_test() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

# Function to make API calls and check response
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local headers=$4
    local description=$5
    
    echo -e "${BLUE}Testing: $description${NC}"
    
    if [ -n "$data" ] && [ -n "$headers" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" "$API_BASE$endpoint" \
            -H "Content-Type: application/json" \
            -H "$headers" \
            -d "$data")
    elif [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" "$API_BASE$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data")
    elif [ -n "$headers" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" "$API_BASE$endpoint" \
            -H "$headers")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$API_BASE$endpoint")
    fi
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [[ "$http_code" =~ ^(200|201|204)$ ]]; then
        print_test 0 "$description"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "$body" | jq . 2>/dev/null || echo "$body"
        fi
        echo ""
        return 0
    else
        echo -e "${RED}HTTP $http_code: $body${NC}"
        print_test 1 "$description"
        return 1
    fi
}

# Check if server is running
echo "🔍 Checking if server is running..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo -e "${RED}❌ Server is not running at $BASE_URL${NC}"
    echo "Please start the server with: python scripts/start_server.py"
    exit 1
fi
print_test 0 "Server health check"

# Test 1: Health Check
test_api "GET" "/../../health" "" "" "Health endpoint"

# Test 2: List entities (should be empty initially or have sample data)
test_api "GET" "/entities/" "" "" "List entities"

# Test 3: Create Address entity
address_entity='{
  "name": "Address",
  "description": "Address entity for testing",
  "core_fields": ["name", "address1", "city", "zipcode"],
  "metadata": {"version": "1.0.0", "test": true}
}'
test_api "POST" "/entities/" "$address_entity" "" "Create Address entity"

# Test 4: Get Address entity
test_api "GET" "/entities/Address" "" "" "Get Address entity"

# Test 5: Create tenants
tenant_a='{
  "name": "Test Corp A",
  "description": "Test company A",
  "metadata": {"industry": "testing"}
}'
tenant_b='{
  "name": "Test Corp B", 
  "description": "Test company B",
  "metadata": {"industry": "testing"}
}'

test_api "POST" "/tenants/" "$tenant_a" "" "Create Tenant A"
test_api "POST" "/tenants/" "$tenant_b" "" "Create Tenant B"

# Get tenant IDs
echo "🔍 Getting tenant IDs..."
tenants_response=$(curl -s "$API_BASE/tenants/")
TENANT_A_ID=$(echo "$tenants_response" | jq -r '.[] | select(.name=="Test Corp A") | .id')
TENANT_B_ID=$(echo "$tenants_response" | jq -r '.[] | select(.name=="Test Corp B") | .id')

if [ -z "$TENANT_A_ID" ] || [ "$TENANT_A_ID" = "null" ]; then
    echo -e "${RED}❌ Could not get Tenant A ID${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Got Tenant A ID: $TENANT_A_ID${NC}"
echo -e "${GREEN}✅ Got Tenant B ID: $TENANT_B_ID${NC}"
echo ""

# Test 6: Add core fields
address2_field='{
  "name": "address2",
  "type": "string",
  "scope": "core",
  "description": "Secondary address line",
  "constraints": {
    "required": false,
    "max_length": 100
  }
}'

country_field='{
  "name": "country",
  "type": "string",
  "scope": "core", 
  "description": "Country code",
  "constraints": {
    "required": true,
    "max_length": 3,
    "pattern": "^[A-Z]{2,3}$",
    "default": "USA"
  }
}'

test_api "POST" "/entities/Address/fields" "$address2_field" "" "Add address2 core field"
test_api "POST" "/entities/Address/fields" "$country_field" "" "Add country core field"

# Test 7: Add tenant-specific fields
building_field='{
  "name": "building_number",
  "type": "string",
  "scope": "tenant",
  "description": "Building number for Tenant A",
  "constraints": {
    "required": true,
    "max_length": 10
  }
}'

district_field='{
  "name": "district",
  "type": "string",
  "scope": "tenant",
  "description": "District for Tenant B",
  "constraints": {
    "required": false,
    "max_length": 50
  }
}'

test_api "POST" "/entities/Address/fields" "$building_field" "X-Tenant-ID: $TENANT_A_ID" "Add building_number for Tenant A"
test_api "POST" "/entities/Address/fields" "$district_field" "X-Tenant-ID: $TENANT_B_ID" "Add district for Tenant B"

# Test 8: List fields from different perspectives
test_api "GET" "/entities/Address/fields" "" "" "List all core fields"
test_api "GET" "/entities/Address/fields" "" "X-Tenant-ID: $TENANT_A_ID" "List fields for Tenant A"
test_api "GET" "/entities/Address/fields" "" "X-Tenant-ID: $TENANT_B_ID" "List fields for Tenant B"

# Test 9: Get specific fields
test_api "GET" "/entities/Address/fields/address2" "" "" "Get address2 field"
test_api "GET" "/entities/Address/fields/building_number" "" "X-Tenant-ID: $TENANT_A_ID" "Get building_number for Tenant A"

# Test 10: Update a field
field_update='{
  "description": "Updated secondary address line",
  "constraints": {
    "required": false,
    "max_length": 150
  }
}'
test_api "PUT" "/entities/Address/fields/address2" "$field_update" "" "Update address2 field"

# Test 11: Tenant configuration
tenant_config='{
  "field_overrides": [
    {
      "field_name": "zipcode",
      "constraints": {
        "required": true,
        "pattern": "^[0-9]{5}$"
      },
      "required": true
    }
  ],
  "ui_settings": {
    "theme": "compact"
  },
  "feature_flags": {
    "enable_validation": true
  }
}'

test_api "PUT" "/tenants/$TENANT_A_ID/entities/Address/config" "$tenant_config" "" "Configure Tenant A overrides"

# Test 12: Get tenant entity view
test_api "GET" "/tenants/$TENANT_A_ID/entities/Address/fields" "" "" "Get Tenant A entity view"

# Test 13: Field promotion
promotion_data='{
  "new_version": "1.3.0",
  "migration_strategy": "copy"
}'
test_api "POST" "/entities/Address/fields/building_number/promote" "$promotion_data" "X-Tenant-ID: $TENANT_A_ID" "Promote building_number to core"

# Test 14: Field deprecation
# First add a field to deprecate
fax_field='{
  "name": "fax_number",
  "type": "string",
  "scope": "core",
  "description": "Fax number (will be deprecated)",
  "constraints": {
    "required": false,
    "max_length": 20
  }
}'
test_api "POST" "/entities/Address/fields" "$fax_field" "" "Add fax_number field"

deprecation_data='{
  "version_deprecated": "1.4.0",
  "reason": "Fax is obsolete",
  "grace_period_days": 90
}'
test_api "POST" "/entities/Address/fields/fax_number/deprecate" "$deprecation_data" "" "Deprecate fax_number field"

# Test 15: Schema and versioning
test_api "GET" "/entities/Address/schema" "" "" "Get Address schema"
test_api "GET" "/entities/Address/versions" "" "" "Get Address schema versions"

# Test 16: List fields with filters
test_api "GET" "/entities/Address/fields?scope=core" "" "" "List only core fields"
test_api "GET" "/entities/Address/fields?include_deprecated=true" "" "" "List fields including deprecated"

echo ""
echo -e "${GREEN}🎉 All API tests passed successfully!${NC}"
echo "================================"
echo ""
echo "📊 Test Summary:"
echo "  • Entity management: ✅"
echo "  • Field management: ✅"
echo "  • Tenant management: ✅"
echo "  • Field lifecycle: ✅"
echo "  • Tenant configuration: ✅"
echo "  • Schema versioning: ✅"
echo ""
echo "🌐 API Documentation: $BASE_URL/docs"
echo "📚 ReDoc Documentation: $BASE_URL/redoc"
