#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the demo after starting the server.
"""

import asyncio
import time
import subprocess
import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from examples.demo_usage import demo_scenario


async def wait_for_server(max_attempts=30):
    """Wait for the server to be ready."""
    import httpx
    
    for attempt in range(max_attempts):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8000/health")
                if response.status_code == 200:
                    print("✅ Server is ready!")
                    return True
        except:
            pass
        
        print(f"⏳ Waiting for server... (attempt {attempt + 1}/{max_attempts})")
        await asyncio.sleep(2)
    
    return False


async def main():
    """Main entry point."""
    print("🎬 Running Metadata Fields Demo")
    print("=" * 50)
    
    # Wait for server to be ready
    if not await wait_for_server():
        print("❌ Server is not responding. Please start it manually:")
        print("   python scripts/start_server.py")
        return
    
    # Run the demo
    await demo_scenario()


if __name__ == "__main__":
    asyncio.run(main())
