#!/usr/bin/env python3
"""
<PERSON>ript to start the Metadata Fields API server.
"""

import asyncio
import sys
import os
import uvicorn

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def main():
    """Main entry point."""
    print("🚀 Starting Metadata Fields API Server")
    print("=" * 50)
    print("📡 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("📖 ReDoc Documentation: http://localhost:8000/redoc")
    print("=" * 50)

    # Start the server
    uvicorn.run(
        "src.metadata_fields.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    main()
