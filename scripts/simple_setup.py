#!/usr/bin/env python3
"""
Simple setup script that creates tables and sample data.
Run this after activating your virtual environment.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import text
    
    # Import our models
    from src.metadata_fields.database.models import (
        Base, EntityType, Tenant, FieldDefinition, 
        TenantConfiguration, SchemaVersion
    )
    from src.metadata_fields.core.config import get_settings
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you have activated your virtual environment and installed dependencies:")
    print("  source venv/bin/activate")
    print("  pip install -r requirements.txt")
    sys.exit(1)


async def setup_database():
    """Set up database with tables and sample data."""
    try:
        print("🚀 Setting up Metadata Fields Database")
        print("=" * 50)
        
        settings = get_settings()
        print(f"📡 Connecting to: {settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else 'database'}")
        
        # Create engine
        engine = create_async_engine(settings.DATABASE_URL, echo=False)
        
        # Create session factory
        AsyncSessionLocal = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # Create tables
        print("🔧 Creating database tables...")
        async with engine.begin() as conn:
            # Drop existing tables
            await conn.run_sync(Base.metadata.drop_all)
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
        print("✅ Database tables created!")
        
        # Create sample data
        print("🔧 Creating sample data...")
        async with AsyncSessionLocal() as session:
            # Create entities
            address_entity = EntityType(
                name="Address",
                description="Address entity for multi-tenant SaaS",
                core_fields=["name", "address1", "city", "zipcode"],
                meta_data={"version": "1.0.0", "created_by": "setup_script"}
            )
            session.add(address_entity)
            
            product_entity = EntityType(
                name="Product",
                description="Product catalog entity",
                core_fields=["name", "price", "description", "sku"],
                meta_data={"version": "1.0.0", "created_by": "setup_script"}
            )
            session.add(product_entity)
            
            # Create tenants
            tenant_acme = Tenant(
                name="ACME Corp",
                description="Manufacturing company",
                meta_data={"industry": "manufacturing", "size": "large"},
                is_active=True
            )
            session.add(tenant_acme)
            
            tenant_tech = Tenant(
                name="TechStart Inc",
                description="Technology startup",
                meta_data={"industry": "technology", "size": "small"},
                is_active=True
            )
            session.add(tenant_tech)
            
            # Commit to get IDs
            await session.commit()
            await session.refresh(tenant_acme)
            await session.refresh(tenant_tech)
            
            # Create some fields
            address2_field = FieldDefinition(
                name="address2",
                entity_type="Address",
                type="string",
                scope="core",
                description="Secondary address line",
                constraints={
                    "required": False,
                    "max_length": 100
                },
                ui_config={
                    "label": "Address Line 2",
                    "placeholder": "Apt, Suite, Unit, etc."
                },
                version_added="1.1.0",
                is_deprecated=False
            )
            session.add(address2_field)
            
            # Create tenant-specific field
            building_field = FieldDefinition(
                name="building_number",
                entity_type="Address",
                type="string",
                scope="tenant",
                description="Building number for ACME Corp",
                constraints={
                    "required": True,
                    "max_length": 10
                },
                tenant_id=tenant_acme.id,
                version_added="1.0.0",
                is_deprecated=False
            )
            session.add(building_field)
            
            # Create schema version
            schema_v1 = SchemaVersion(
                entity_type="Address",
                version="1.0.0",
                description="Initial Address entity",
                changes=["Created Address entity"],
                is_current=True
            )
            session.add(schema_v1)
            
            await session.commit()
            
        print("✅ Sample data created!")
        
        # Test the setup
        print("🧪 Testing database connection...")
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT COUNT(*) FROM entity_types"))
            count = result.scalar()
            if count > 0:
                print(f"✅ Found {count} entity types in database")
            else:
                print("⚠️  No entity types found")
        
        await engine.dispose()
        
        print("\n🎉 Database setup completed successfully!")
        print("=" * 50)
        print("📊 Created:")
        print("  • 2 Entity Types (Address, Product)")
        print("  • 2 Tenants (ACME Corp, TechStart Inc)")
        print("  • 2 Field Definitions")
        print("  • 1 Schema Version")
        print("\nYou can now start the server with:")
        print("  python scripts/start_server.py")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(setup_database())
