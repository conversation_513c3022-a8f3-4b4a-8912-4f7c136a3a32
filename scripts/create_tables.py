#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create database tables directly without <PERSON><PERSON><PERSON>.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from sqlalchemy.ext.asyncio import create_async_engine
from src.metadata_fields.database.models import Base
from src.metadata_fields.core.config import get_settings


async def create_tables():
    """Create all database tables."""
    try:
        print("🔧 Creating database tables...")
        
        settings = get_settings()
        engine = create_async_engine(settings.DATABASE_URL, echo=False)
        
        async with engine.begin() as conn:
            # Drop all tables first (for clean setup)
            await conn.run_sync(Base.metadata.drop_all)
            print("✅ Dropped existing tables")
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            print("✅ Created all tables")
        
        await engine.dispose()
        print("✅ Database tables created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(create_tables())
